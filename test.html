<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .search-box { margin: 20px 0; }
        #search-input { padding: 10px; width: 300px; }
        #search-button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; }
        .loading { color: #666; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>短剧搜索测试</h1>
    
    <div class="search-box">
        <input type="text" id="search-input" placeholder="输入短剧名称..." value="总裁">
        <button id="search-button">搜索</button>
    </div>
    
    <div id="status"></div>
    <div id="results"></div>

    <script>
        console.log('页面加载完成');
        
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        
        async function fetchData(params) {
            console.log('开始请求API:', params);
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            console.log('请求URL:', url.toString());
            
            try {
                const response = await fetch(url.toString());
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                return data;
            } catch (error) {
                console.error("请求错误:", error);
                return null;
            }
        }
        
        async function searchTest() {
            const searchInput = document.getElementById('search-input');
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            const searchTerm = searchInput.value.trim();
            console.log('搜索关键词:', searchTerm);
            
            if (!searchTerm) {
                alert('请输入搜索关键词');
                return;
            }
            
            statusDiv.innerHTML = '<div class="loading">正在搜索...</div>';
            resultsDiv.innerHTML = '';
            
            const data = await fetchData({ name: searchTerm, page: 1 });
            
            if (data) {
                statusDiv.innerHTML = '<div style="color: green;">搜索成功</div>';
                
                if (data.data && data.data.length > 0) {
                    resultsDiv.innerHTML = data.data.map(item => {
                        // 处理封面图片URL
                        let coverUrl = item.cover || '';
                        if (coverUrl.includes('&cenguigui_api')) {
                            coverUrl = coverUrl.split('&cenguigui_api')[0];
                        }

                        return `
                        <div class="result-item" onclick="goToPlayer('${item.book_id}')" style="cursor: pointer;">
                            <img src="${coverUrl}" alt="${item.title}" style="width: 150px; height: 200px; object-fit: cover;" onerror="this.src='data:image/svg+xml,<svg xmlns=\\"http://www.w3.org/2000/svg\\" viewBox=\\"0 0 100 100\\"><text y=\\".9em\\" font-size=\\"90\\">🎬</text></svg>'">
                            <h3>${item.title}</h3>
                            <p>作者: ${item.author}</p>
                            <p>类型: ${item.type}</p>
                            <p>播放量: ${item.play_cnt}</p>
                            <p>集数: ${item.episode_cnt}</p>
                            <p>简介: ${item.intro}</p>
                            <button onclick="event.stopPropagation(); goToPlayer('${item.book_id}')" style="margin-top: 10px; padding: 5px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">播放</button>
                        </div>
                    `;}).join('');
                } else {
                    resultsDiv.innerHTML = '<div>没有找到结果</div>';
                }
            } else {
                statusDiv.innerHTML = '<div class="error">搜索失败</div>';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            
            const searchButton = document.getElementById('search-button');
            const searchInput = document.getElementById('search-input');
            
            searchButton.addEventListener('click', function() {
                console.log('点击搜索按钮');
                searchTest();
            });
            
            searchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    console.log('按下回车键');
                    searchTest();
                }
            });
            
            console.log('事件监听器已添加');
        });

        // 播放功能
        window.goToPlayer = function(book_id) {
            console.log('跳转到播放页面:', book_id);
            window.open(`player.html?book_id=${book_id}`, '_blank');
        };
    </script>
</body>
</html>
