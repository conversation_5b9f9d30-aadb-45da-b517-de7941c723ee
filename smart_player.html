<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .player-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .play-area {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .play-area:hover {
            transform: scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        .play-content {
            z-index: 2;
        }
        .play-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .play-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .play-subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 25px;
        }
        .action-card {
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .action-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        .action-card.primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .action-card.primary:hover {
            border-color: #28a745;
        }
        .action-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        .action-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .action-desc {
            font-size: 12px;
            opacity: 0.7;
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            max-height: 500px;
            overflow-y: auto;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .episode {
            padding: 15px;
            margin: 10px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .episode-info {
            flex: 1;
        }
        .episode-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 5px;
        }
        .episode-meta {
            font-size: 12px;
            opacity: 0.7;
        }
        .episode-actions {
            display: flex;
            gap: 8px;
        }
        .mini-btn {
            padding: 6px 12px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            color: inherit;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }
        .mini-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        .loading { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #666;
            font-size: 18px;
            flex-direction: column;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e8ed;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 12px 20px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
            text-align: center;
            font-size: 14px;
        }
        .quality-selector {
            display: flex;
            gap: 8px;
            margin: 15px 0;
            justify-content: center;
        }
        .quality-btn {
            padding: 8px 15px;
            background: rgba(255,255,255,0.8);
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .quality-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }
        .quality-btn:hover {
            border-color: #667eea;
        }
        @media (max-width: 768px) {
            .player-container {
                grid-template-columns: 1fr;
            }
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            正在加载短剧信息...
        </div>
        
        <div id="content" style="display: none;">
            <div class="player-container">
                <div class="video-section">
                    <div class="play-area" onclick="copyCurrentLink()">
                        <div class="play-content">
                            <div class="play-icon">🎬</div>
                            <div class="play-title" id="currentEpisodeTitle">选择一集开始播放</div>
                            <div class="play-subtitle">点击复制播放链接</div>
                        </div>
                    </div>
                    
                    <div class="quality-selector">
                        <button class="quality-btn" onclick="setQuality('360p')" id="q-360p">360p</button>
                        <button class="quality-btn" onclick="setQuality('480p')" id="q-480p">480p</button>
                        <button class="quality-btn" onclick="setQuality('720p')" id="q-720p">720p</button>
                        <button class="quality-btn active" onclick="setQuality('1080p')" id="q-1080p">1080p</button>
                        <button class="quality-btn" onclick="setQuality('2160p')" id="q-2160p">2160p</button>
                    </div>
                    
                    <div class="quick-actions">
                        <div class="action-card primary" onclick="copyCurrentLink()">
                            <div class="action-icon">📋</div>
                            <div class="action-title">复制播放链接</div>
                            <div class="action-desc">推荐方式，复制后在播放器中播放</div>
                        </div>
                        
                        <div class="action-card" onclick="downloadCurrent()">
                            <div class="action-icon">📥</div>
                            <div class="action-title">下载视频</div>
                            <div class="action-desc">下载到本地观看</div>
                        </div>
                        
                        <div class="action-card" onclick="openInVLC()">
                            <div class="action-icon">🎮</div>
                            <div class="action-title">VLC播放</div>
                            <div class="action-desc">尝试在VLC中打开</div>
                        </div>
                        
                        <div class="action-card" onclick="copyAllLinks()">
                            <div class="action-icon">📑</div>
                            <div class="action-title">复制全部</div>
                            <div class="action-desc">复制所有集的播放链接</div>
                        </div>
                    </div>
                    
                    <div id="copySuccess" class="success-msg" style="display: none;">
                        ✅ 链接已复制！请在PotPlayer、VLC等播放器中粘贴播放
                    </div>
                </div>
                
                <div class="drama-info">
                    <h2 id="dramaTitle">加载中...</h2>
                    <p id="dramaDesc">正在加载剧集信息...</p>
                    <div id="dramaMeta"></div>
                </div>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        let currentVideoId = null;
        let currentQuality = '1080p';
        let currentEpisodeTitle = '';
        let dramaData = null;
        let allVideoUrls = {};
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                alert('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                    
                    // 默认选择第一集
                    if (data.data.length > 0) {
                        selectEpisode(data.data[0].video_id, 0, data.data[0].title || '第1集');
                    }
                } else {
                    alert('无法加载短剧信息');
                }
            } catch (error) {
                alert('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div class="episode-info">
                        <div class="episode-title">${video.title || `第${index + 1}集`}</div>
                        <div class="episode-meta">点击选择播放</div>
                    </div>
                    <div class="episode-actions">
                        <button class="mini-btn" onclick="event.stopPropagation(); quickCopy('${video.video_id}', '${video.title || `第${index + 1}集`}')">复制</button>
                        <button class="mini-btn" onclick="event.stopPropagation(); quickDownload('${video.video_id}')">下载</button>
                    </div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            currentEpisodeTitle = title;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
                const meta = ep.querySelector('.episode-meta');
                if (i === index) {
                    meta.textContent = '当前选中';
                } else {
                    meta.textContent = '点击选择播放';
                }
            });
            
            // 更新播放区域
            document.getElementById('currentEpisodeTitle').textContent = title;
        }
        
        function setQuality(quality) {
            currentQuality = quality;
            
            // 更新按钮状态
            document.querySelectorAll('.quality-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`q-${quality}`).classList.add('active');
        }
        
        function getCurrentVideoUrl() {
            if (!currentVideoId) return null;
            return `${API_BASE_URL}?video_id=${currentVideoId}&type=mp4&level=${currentQuality}`;
        }
        
        function copyCurrentLink() {
            const url = getCurrentVideoUrl();
            if (!url) {
                alert('请先选择一集');
                return;
            }
            
            navigator.clipboard.writeText(url).then(() => {
                showCopySuccess();
            }).catch(() => {
                prompt('请手动复制播放链接:', url);
            });
        }
        
        async function quickCopy(videoId, title) {
            const url = `${API_BASE_URL}?video_id=${videoId}&type=mp4&level=${currentQuality}`;
            navigator.clipboard.writeText(url).then(() => {
                alert(`${title} 播放链接已复制`);
            }).catch(() => {
                prompt(`请手动复制 ${title} 播放链接:`, url);
            });
        }
        
        function downloadCurrent() {
            if (!currentVideoId) {
                alert('请先选择一集');
                return;
            }
            const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url&level=${currentQuality}`;
            window.open(downloadUrl, '_blank');
        }
        
        function quickDownload(videoId) {
            const downloadUrl = `${API_BASE_URL}?video_id=${videoId}&type=url&level=${currentQuality}`;
            window.open(downloadUrl, '_blank');
        }
        
        function openInVLC() {
            const url = getCurrentVideoUrl();
            if (!url) {
                alert('请先选择一集');
                return;
            }
            
            // 尝试打开VLC
            const vlcUrl = `vlc://${url}`;
            window.open(vlcUrl, '_blank');
            
            setTimeout(() => {
                if (confirm('如果VLC没有自动打开，是否复制链接？')) {
                    copyCurrentLink();
                }
            }, 2000);
        }
        
        function copyAllLinks() {
            if (!dramaData) return;
            
            let allLinks = `${dramaData.book_name} - 全集播放链接 (${currentQuality})\n\n`;
            
            dramaData.data.forEach((video, index) => {
                const url = `${API_BASE_URL}?video_id=${video.video_id}&type=mp4&level=${currentQuality}`;
                allLinks += `${video.title || `第${index + 1}集`}：\n${url}\n\n`;
            });
            
            navigator.clipboard.writeText(allLinks).then(() => {
                alert('所有集播放链接已复制到剪贴板');
            }).catch(() => {
                const textarea = document.createElement('textarea');
                textarea.value = allLinks;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('所有集播放链接已复制到剪贴板');
            });
        }
        
        function showCopySuccess() {
            const successMsg = document.getElementById('copySuccess');
            successMsg.style.display = 'block';
            setTimeout(() => {
                successMsg.style.display = 'none';
            }, 3000);
        }
        
        // 页面加载完成后自动加载短剧
        document.addEventListener('DOMContentLoaded', loadDrama);
    </script>
</body>
</html>
