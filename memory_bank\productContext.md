# 产品上下文
本项目旨在根据提供的API文档创建一个功能完善的短剧搜索页面。该页面将支持短剧的搜索、详情查看、视频播放列表、以及推荐功能。API支持番茄、蛋花、西瓜、悟空、常读、红果等多个平台的聚合短剧资源。
短剧解析接口文档
目前支持：番茄/蛋花/西瓜/悟空/常读/红果 聚合短剧
说明：搜索，详情，视频，推荐

笒鬼鬼Q：2963246343

请求地址：https://api.cenguigui.cn/api/duanju/api.php?name=短剧名&showRawParams=是否返回原始参数

请求方法：GET/POST

请求参数：

参数名称	参数类型	是否必填	默认值	参数描述
搜索参数
name	string	是	无	短剧名
page	string	是	无	下一页，每页有10组数据
showRawParams	string	否	false	false和true，是否显示原始参数，默认false
调用示例：https://api.cenguigui.cn/api/duanju/api.php?name=总裁&page=1&showRawParams=false
视频列表参数
book_id	int	是	无	book_id参数的ID，默认返回全部
showRawParams	string	否	false	false和true，是否显示原始参数，默认false
调用示例：https://api.cenguigui.cn/api/duanju/api.php?book_id=7416545333695499326&showRawParams=false
视频详情演员解析参数
series_id	int	是	无	book_id的ID
showRawParams	string	否	false	false和true，是否显示原始参数，默认false
调用示例：https://api.cenguigui.cn/api/duanju/api.php?series_id=7512797350914444313&showRawParams=false
视频参数
video_id	int	是	无	video_id的ID
level	string	否	1080p	360p/480p/720p/1080p/2160p，默认1080p，注意(2160p不是每个视频都有没有的返回高频率的1080p,简单说就是很大的1080p)
showRawParams	string	否	false	false和true，是否显示原始参数，默认false
调用示例：https://api.cenguigui.cn/api/duanju/api.php?video_id=7416589528229497881&type=json&showRawParams=false
画质调用示例：https://api.cenguigui.cn/api/duanju/api.php?video_id=7416589528229497881&type=json&level=2160p
跳转封面：https://api.cenguigui.cn/api/duanju/api.php?video_id=7416589528229497881&type=pic
跳转视频：https://api.cenguigui.cn/api/duanju/api.php?video_id=7416589528229497881&type=mp4
触发下载：https://api.cenguigui.cn/api/duanju/api.php?video_id=7416589528229497881&type=url
分类参数
name	int	是	无	推荐榜,新剧,逆袭,霸总,现代言情,打脸虐渣,豪门恩怨,神豪,马甲,都市日常,战神归来,小人物,女性成长,大女主,穿越,都市修仙,强者回归,亲情,古装,重生,闪婚,赘婿逆袭,虐恋,追妻,天下无敌,家庭伦理,萌宝,古风权谋,职场,奇幻脑洞,异能,无敌神医,古风言情,传承觉醒,现言甜宠,奇幻爱情,乡村,历史古代,王妃,高手下山,娱乐圈,强强联合,破镜重圆,暗恋成真,民国,欢喜冤家,系统,真假千金,龙王,校园,穿书,女帝,团宠,年代爱情,玄幻仙侠,青梅竹马,悬疑推理,皇后,替身,大叔,喜剧,剧情
offset	int	是	无	翻页，默认1
showRawParams	string	否	false	false和true，是否显示原始参数，默认false
调用示例：https://api.cenguigui.cn/api/duanju/api.php?classname=穿越&offset=20&showRawParams=false
推荐参数
type	int	是	无	recommend，随机推荐
showRawParams	string	否	false	false和true，是否显示原始参数，默认false
调用示例：https://api.cenguigui.cn/api/duanju/api.php?type=recommend&showRawParams=false
#响应：
响应数据类型：JSON

响应结果名称	响应结果类型	响应结果描述
code	int	解析状态码
title	string	提示信息
type	string	解析类型
data	json	具体解析数据，详细字段请看响应示例
© 2024 笒鬼鬼 | Power by cenguigui

