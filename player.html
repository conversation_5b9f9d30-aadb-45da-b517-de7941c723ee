<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短剧播放器</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 返回按钮 -->
        <div style="margin-bottom: 20px;">
            <button onclick="history.back()" style="padding: 10px 20px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 20px; cursor: pointer;">
                ← 返回搜索
            </button>
        </div>

        <div class="player-container">
            <div class="video-player">
                <video id="videoPlayer" controls autoplay preload="metadata"></video>

                <!-- 视频控制栏 -->
                <div class="video-controls">
                    <select id="qualitySelector" class="quality-selector">
                        <option value="360p">360p 流畅</option>
                        <option value="480p">480p 标清</option>
                        <option value="720p">720p 高清</option>
                        <option value="1080p" selected>1080p 超清</option>
                        <option value="2160p">2160p 4K</option>
                    </select>

                    <button id="downloadBtn" class="quality-selector" style="margin-left: auto;">
                        📥 下载
                    </button>
                </div>
            </div>

            <div class="video-details">
                <h2 id="videoTitle">加载中...</h2>
                <div id="videoMeta" style="margin: 15px 0; color: #7f8c8d; font-size: 14px;"></div>
                <p id="videoDescription">正在加载剧集信息...</p>

                <!-- 演员信息 -->
                <div id="actorInfo" style="margin-top: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">🎭 演员阵容</h4>
                    <div id="actorList"></div>
                </div>
            </div>
        </div>

        <div class="playlist-container">
            <h3>📺 剧集列表</h3>
            <div style="margin-bottom: 15px; color: #7f8c8d; font-size: 14px;">
                共 <span id="episodeCount">0</span> 集
            </div>
            <ul id="playlist"></ul>
        </div>
    </div>

    <script src="api.js"></script>
    <script src="player.js"></script>
</body>
</html>