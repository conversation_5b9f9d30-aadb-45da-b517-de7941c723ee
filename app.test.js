const { app } = require('./app');
const api = require('./api');

jest.mock('./api');

describe('app.js', () => {
  let mockElements;

  beforeEach(() => {
    document.body.innerHTML = `
      <input id="search-input" />
      <button id="search-button"></button>
      <div id="results-container"></div>
      <div id="pagination"></div>
    `;
    mockElements = {
      searchInput: document.getElementById('search-input'),
      searchButton: document.getElementById('search-button'),
      resultsContainer: document.getElementById('results-container'),
      paginationContainer: document.getElementById('pagination'),
    };
    app._setDomElements(mockElements);
  });

  afterEach(() => {
    document.body.innerHTML = '';
    jest.clearAllMocks();
  });

  describe('searchDuanju', () => {
    it('should call api.fetchData with correct parameters', async () => {
      api.fetchData.mockResolvedValue({ data: { list: [], page: 1, hasMore: false } });
      await app.searchDuanju('test', 1);
      expect(api.fetchData).toHaveBeenCalledWith({ name: 'test', page: 1 });
    });
  });

  describe('renderResults', () => {
    it('should render results to the DOM', () => {
      const items = [{ book_id: '1', title: 'Test Title', cover: 'test.jpg' }];
      app.renderResults(items);
      expect(mockElements.resultsContainer.innerHTML).toContain('Test Title');
    });
  });

  describe('renderPagination', () => {
    it('should render pagination buttons', () => {
      app.renderPagination(1, true);
      expect(mockElements.paginationContainer.innerHTML).toContain('下一页');
    });
  });

  describe('loadRecommendations', () => {
    it('should load and render recommendations', async () => {
      api.fetchData.mockResolvedValue({ data: { list: [{ book_id: '2', title: 'Rec Title', cover: 'rec.jpg' }] } });
      await app.loadRecommendations();
      expect(mockElements.resultsContainer.innerHTML).toContain('Rec Title');
    });
  });

  describe('initializeApp', () => {
    it('should initialize the app', () => {
      const spyLoadRecs = jest.spyOn(app, 'loadRecommendations').mockImplementation(() => {});
      const spySearch = jest.spyOn(app, 'searchDuanju');
      
      app.initializeApp();
      
      mockElements.searchInput.value = 'test search';
      mockElements.searchButton.click();
      
      expect(spyLoadRecs).toHaveBeenCalled();
      expect(spySearch).toHaveBeenCalledWith('test search', 1);
    });
  });
});