<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      All files
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">73.33% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>11/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">66.67% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>4/6</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">71.43% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>5/7</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">81.82% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>9/11</span>
      </div>
    </div>
    <p class="quiet">
      Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
    </p>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="api.js"><a href="api.js.html">api.js</a></td>
	<td data-value="73.33" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 73%;"></div><div class="cover-empty" style="width:27%;"></div></div></td>
	<td data-value="73.33" class="pct medium">73.33%</td>
	<td data-value="15" class="abs medium">11/15</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="71.43" class="pct medium">71.43%</td>
	<td data-value="7" class="abs medium">5/7</td>
	<td data-value="81.82" class="pct high">81.82%</td>
	<td data-value="11" class="abs high">9/11</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Tue Oct 02 2018 15:04:40 GMT+0100 (BST)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
<script src="block-navigation.js"></script>
</body>
</html>
