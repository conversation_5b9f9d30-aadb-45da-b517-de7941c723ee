<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1756001190769" clover="3.2.0">
  <project timestamp="1756001190769" name="All files">
    <metrics statements="109" coveredstatements="105" conditionals="44" coveredconditionals="36" methods="22" coveredmethods="19" elements="175" coveredelements="160" complexity="0" loc="109" ncloc="109" packages="1" files="1" classes="1"/>
    <file name="app.js" path="C:\Users\<USER>\Documents\dunju\app.js">
      <metrics statements="109" coveredstatements="105" conditionals="44" coveredconditionals="36" methods="22" coveredmethods="19"/>
      <line num="2" count="6" type="stmt"/>
      <line num="3" count="6" type="stmt"/>
      <line num="4" count="6" type="stmt"/>
      <line num="12" count="21" type="stmt"/>
      <line num="13" count="32" type="stmt"/>
      <line num="14" count="21" type="stmt"/>
      <line num="15" count="21" type="stmt"/>
      <line num="16" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="17" count="1" type="stmt"/>
      <line num="19" count="18" type="stmt"/>
      <line num="21" count="5" type="stmt"/>
      <line num="22" count="5" type="cond" truecount="1" falsecount="1"/>
      <line num="23" count="5" type="stmt"/>
      <line num="25" count="5" type="stmt"/>
      <line num="30" count="5" type="stmt"/>
      <line num="31" count="5" type="stmt"/>
      <line num="32" count="5" type="stmt"/>
      <line num="34" count="5" type="cond" truecount="2" falsecount="0"/>
      <line num="35" count="4" type="cond" truecount="4" falsecount="0"/>
      <line num="36" count="3" type="stmt"/>
      <line num="37" count="3" type="stmt"/>
      <line num="39" count="1" type="stmt"/>
      <line num="40" count="1" type="stmt"/>
      <line num="46" count="3" type="stmt"/>
      <line num="47" count="3" type="cond" truecount="4" falsecount="0"/>
      <line num="48" count="2" type="stmt"/>
      <line num="50" count="2" type="cond" truecount="3" falsecount="1"/>
      <line num="51" count="2" type="stmt"/>
      <line num="52" count="2" type="stmt"/>
      <line num="58" count="9" type="stmt"/>
      <line num="59" count="9" type="stmt"/>
      <line num="60" count="5" type="stmt"/>
      <line num="61" count="5" type="stmt"/>
      <line num="62" count="5" type="stmt"/>
      <line num="63" count="5" type="stmt"/>
      <line num="68" count="5" type="stmt"/>
      <line num="69" count="5" type="stmt"/>
      <line num="74" count="6" type="stmt"/>
      <line num="75" count="6" type="cond" truecount="2" falsecount="0"/>
      <line num="76" count="2" type="stmt"/>
      <line num="77" count="2" type="stmt"/>
      <line num="78" count="2" type="stmt"/>
      <line num="79" count="0" type="stmt"/>
      <line num="81" count="2" type="stmt"/>
      <line num="83" count="6" type="stmt"/>
      <line num="84" count="6" type="stmt"/>
      <line num="85" count="6" type="stmt"/>
      <line num="87" count="6" type="cond" truecount="2" falsecount="0"/>
      <line num="88" count="5" type="stmt"/>
      <line num="89" count="5" type="stmt"/>
      <line num="90" count="5" type="stmt"/>
      <line num="91" count="0" type="stmt"/>
      <line num="93" count="5" type="stmt"/>
      <line num="98" count="2" type="stmt"/>
      <line num="99" count="2" type="stmt"/>
      <line num="100" count="2" type="stmt"/>
      <line num="102" count="2" type="stmt"/>
      <line num="104" count="2" type="stmt"/>
      <line num="111" count="2" type="stmt"/>
      <line num="113" count="2" type="stmt"/>
      <line num="114" count="2" type="stmt"/>
      <line num="115" count="0" type="stmt"/>
      <line num="116" count="0" type="stmt"/>
      <line num="125" count="6" type="stmt"/>
      <line num="127" count="6" type="stmt"/>
      <line num="129" count="6" type="stmt"/>
      <line num="131" count="6" type="stmt"/>
      <line num="133" count="6" type="cond" truecount="5" falsecount="0"/>
      <line num="134" count="2" type="stmt"/>
      <line num="135" count="2" type="stmt"/>
      <line num="142" count="4" type="stmt"/>
      <line num="145" count="4" type="stmt"/>
      <line num="146" count="4" type="stmt"/>
      <line num="151" count="6" type="stmt"/>
      <line num="152" count="6" type="cond" truecount="3" falsecount="1"/>
      <line num="153" count="6" type="stmt"/>
      <line num="161" count="5" type="stmt"/>
      <line num="162" count="5" type="stmt"/>
      <line num="163" count="5" type="stmt"/>
      <line num="164" count="5" type="stmt"/>
      <line num="165" count="5" type="stmt"/>
      <line num="166" count="5" type="stmt"/>
      <line num="167" count="5" type="stmt"/>
      <line num="170" count="5" type="stmt"/>
      <line num="171" count="2" type="stmt"/>
      <line num="172" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="173" count="2" type="stmt"/>
      <line num="177" count="5" type="stmt"/>
      <line num="178" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="179" count="1" type="stmt"/>
      <line num="183" count="5" type="stmt"/>
      <line num="184" count="1" type="stmt"/>
      <line num="185" count="1" type="stmt"/>
      <line num="188" count="5" type="stmt"/>
      <line num="189" count="15" type="cond" truecount="2" falsecount="0"/>
      <line num="190" count="1" type="stmt"/>
      <line num="191" count="1" type="stmt"/>
      <line num="196" count="5" type="stmt"/>
      <line num="201" count="6" type="cond" truecount="3" falsecount="1"/>
      <line num="202" count="6" type="stmt"/>
      <line num="213" count="1" type="stmt"/>
      <line num="214" count="1" type="stmt"/>
      <line num="215" count="1" type="stmt"/>
      <line num="216" count="1" type="stmt"/>
      <line num="217" count="1" type="stmt"/>
      <line num="218" count="1" type="stmt"/>
      <line num="219" count="1" type="stmt"/>
      <line num="226" count="6" type="cond" truecount="1" falsecount="1"/>
      <line num="227" count="6" type="stmt"/>
    </file>
  </project>
</coverage>
