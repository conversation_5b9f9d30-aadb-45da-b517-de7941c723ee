<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .play-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .play-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        .play-btn:hover { transform: translateY(-2px); }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-info { background: #17a2b8; color: white; }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .episode {
            padding: 12px;
            margin: 8px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(5px);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .loading { text-align: center; color: #666; padding: 40px; }
        .error { text-align: center; color: #e74c3c; padding: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div id="loading" class="loading">正在加载短剧信息...</div>
        
        <div id="content" style="display: none;">
            <div id="dramaInfo" class="drama-info">
                <h2 id="dramaTitle">加载中...</h2>
                <p id="dramaDesc">正在加载剧集信息...</p>
                <div id="dramaMeta"></div>
            </div>
            
            <div class="play-options">
                <button class="play-btn btn-primary" onclick="playInNewTab()">
                    🔗 新窗口播放
                </button>
                <button class="play-btn btn-success" onclick="downloadCurrent()">
                    📥 下载当前集
                </button>
                <button class="play-btn btn-warning" onclick="copyCurrent()">
                    📋 复制链接
                </button>
                <button class="play-btn btn-info" onclick="showVideoInfo()">
                    ℹ️ 视频信息
                </button>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        let currentVideoId = null;
        let currentVideoUrl = null;
        let dramaData = null;
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                showError('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                    
                    // 默认选择第一集
                    if (data.data.length > 0) {
                        selectEpisode(data.data[0].video_id, 0);
                    }
                } else {
                    showError('无法加载短剧信息');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${video.title || `第${index + 1}集`}</span>
                        <span style="font-size: 12px;">▶</span>
                    </div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index);
                playlist.appendChild(episode);
            });
        }
        
        async function selectEpisode(videoId, index) {
            currentVideoId = videoId;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
            });
            
            // 获取视频URL
            try {
                const videoData = await fetchData({ 
                    video_id: videoId, 
                    type: 'json',
                    level: '1080p'
                });
                
                if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                    currentVideoUrl = videoData.data.url;
                    console.log('当前视频URL:', currentVideoUrl);
                } else {
                    currentVideoUrl = null;
                    console.error('无法获取视频URL');
                }
            } catch (error) {
                console.error('获取视频URL失败:', error);
                currentVideoUrl = null;
            }
        }
        
        function playInNewTab() {
            if (currentVideoUrl) {
                window.open(currentVideoUrl, '_blank');
            } else {
                alert('请先选择一集');
            }
        }
        
        function downloadCurrent() {
            if (currentVideoId) {
                const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url`;
                window.open(downloadUrl, '_blank');
            } else {
                alert('请先选择一集');
            }
        }
        
        function copyCurrent() {
            if (currentVideoUrl) {
                navigator.clipboard.writeText(currentVideoUrl).then(() => {
                    alert('视频链接已复制到剪贴板');
                }).catch(() => {
                    prompt('请手动复制视频链接:', currentVideoUrl);
                });
            } else {
                alert('请先选择一集');
            }
        }
        
        function showVideoInfo() {
            if (currentVideoId && currentVideoUrl) {
                const info = `
视频ID: ${currentVideoId}
视频URL: ${currentVideoUrl}
API地址: ${API_BASE_URL}?video_id=${currentVideoId}&type=json
下载地址: ${API_BASE_URL}?video_id=${currentVideoId}&type=url
                `;
                alert(info);
            } else {
                alert('请先选择一集');
            }
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = `
                <h3>❌ ${message}</h3>
                <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                    重新加载
                </button>
            `;
        }
        
        // 页面加载完成后自动加载短剧
        document.addEventListener('DOMContentLoaded', loadDrama);
    </script>
</body>
</html>
