<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页视频播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .status-bar {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .player-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .video-player {
            width: 100%;
            height: 450px;
            background: #000;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 15px;
        }
        .player-controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .control-btn {
            padding: 10px 18px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .control-btn.active {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .control-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            max-height: 500px;
            overflow-y: auto;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .episode {
            padding: 15px;
            margin: 10px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .episode.loading {
            opacity: 0.6;
            cursor: wait;
        }
        .episode-title {
            font-weight: 600;
            font-size: 16px;
        }
        .episode-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
        }
        .loading { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 450px;
            color: #666;
            font-size: 18px;
            flex-direction: column;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e8ed;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 450px;
            color: #e74c3c;
            font-size: 18px;
            text-align: center;
            flex-direction: column;
        }
        .method-selector {
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .method-btn {
            padding: 8px 15px;
            margin: 5px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .method-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }
        .method-btn:hover {
            border-color: #667eea;
        }
        @media (max-width: 768px) {
            .player-container {
                grid-template-columns: 1fr;
            }
            .video-player {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div class="status-bar">
            <span class="status-indicator" id="swStatus"></span>
            <span id="swStatusText">正在初始化播放器...</span>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            正在加载短剧信息...
        </div>
        
        <div id="content" style="display: none;">
            <div class="player-container">
                <div class="video-section">
                    <div class="video-player" id="videoContainer">
                        <div class="loading">
                            <div class="spinner"></div>
                            选择一集开始播放
                        </div>
                    </div>
                    
                    <div class="method-selector">
                        <h4 style="margin: 0 0 10px 0;">🎬 播放方式</h4>
                        <button class="method-btn active" onclick="setPlayMethod('sw')" id="method-sw">Service Worker</button>
                        <button class="method-btn" onclick="setPlayMethod('blob')" id="method-blob">Blob URL</button>
                        <button class="method-btn" onclick="setPlayMethod('direct')" id="method-direct">直接播放</button>
                        <button class="method-btn" onclick="setPlayMethod('iframe')" id="method-iframe">iframe嵌入</button>
                    </div>
                    
                    <div class="player-controls">
                        <button class="control-btn active" onclick="changeQuality('1080p')" id="btn-1080p">1080p</button>
                        <button class="control-btn" onclick="changeQuality('720p')" id="btn-720p">720p</button>
                        <button class="control-btn" onclick="changeQuality('480p')" id="btn-480p">480p</button>
                        <button class="control-btn" onclick="changeQuality('360p')" id="btn-360p">360p</button>
                        <button class="control-btn" onclick="downloadCurrent()">📥 下载</button>
                        <button class="control-btn" onclick="copyLink()">📋 复制链接</button>
                    </div>
                </div>
                
                <div class="drama-info">
                    <h2 id="dramaTitle">加载中...</h2>
                    <p id="dramaDesc">正在加载剧集信息...</p>
                    <div id="dramaMeta"></div>
                </div>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        let currentVideoId = null;
        let currentQuality = '1080p';
        let currentPlayMethod = 'sw';
        let dramaData = null;
        let serviceWorkerReady = false;
        
        // Initialize Service Worker
        async function initServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.register('./service-worker.js');
                    console.log('Service Worker registered:', registration);
                    
                    await navigator.serviceWorker.ready;
                    serviceWorkerReady = true;
                    updateStatus('online', '✅ Service Worker已就绪，支持网页播放');
                    
                } catch (error) {
                    console.error('Service Worker registration failed:', error);
                    serviceWorkerReady = false;
                    updateStatus('offline', '❌ Service Worker注册失败，将使用备用方案');
                }
            } else {
                updateStatus('offline', '❌ 浏览器不支持Service Worker');
            }
        }
        
        function updateStatus(status, text) {
            const indicator = document.getElementById('swStatus');
            const statusText = document.getElementById('swStatusText');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                showError('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                } else {
                    showError('无法加载短剧信息');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div class="episode-title">${video.title || `第${index + 1}集`}</div>
                    <div class="episode-status">▶</div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.remove('active', 'loading');
                const status = ep.querySelector('.episode-status');
                if (i === index) {
                    ep.classList.add('active');
                    status.textContent = '播放中';
                } else {
                    status.textContent = '▶';
                }
            });
            
            // 播放视频
            playVideo(videoId);
        }
        
        async function playVideo(videoId) {
            const container = document.getElementById('videoContainer');
            
            // 显示加载状态
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    正在加载视频...
                </div>
            `;
            
            try {
                // 获取视频URL
                const videoData = await fetchData({ 
                    video_id: videoId, 
                    type: 'json',
                    level: currentQuality 
                });
                
                if (!videoData || videoData.code !== 200 || !videoData.data || !videoData.data.url) {
                    throw new Error('无法获取视频URL');
                }
                
                const videoUrl = videoData.data.url;
                console.log('原始视频URL:', videoUrl);
                
                // 根据播放方式处理
                switch (currentPlayMethod) {
                    case 'sw':
                        await playWithServiceWorker(videoUrl, container);
                        break;
                    case 'blob':
                        await playWithBlob(videoUrl, container);
                        break;
                    case 'direct':
                        playDirect(videoUrl, container);
                        break;
                    case 'iframe':
                        playWithIframe(videoUrl, container);
                        break;
                }
                
            } catch (error) {
                console.error('播放视频失败:', error);
                showVideoError(error.message);
            }
        }
        
        async function playWithServiceWorker(videoUrl, container) {
            if (!serviceWorkerReady) {
                throw new Error('Service Worker未就绪');
            }
            
            const proxyUrl = `${window.location.origin}${window.location.pathname}?proxy_video=${encodeURIComponent(videoUrl)}`;
            
            container.innerHTML = `
                <video controls autoplay preload="metadata" style="width: 100%; height: 100%;">
                    <source src="${proxyUrl}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            video.onerror = () => {
                throw new Error('Service Worker播放失败');
            };
        }
        
        async function playWithBlob(videoUrl, container) {
            try {
                updateStatus('loading', '正在下载视频数据...');
                
                const response = await fetch(videoUrl, {
                    mode: 'cors',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.douyin.com/'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const blob = await response.blob();
                const blobUrl = URL.createObjectURL(blob);
                
                container.innerHTML = `
                    <video controls autoplay preload="metadata" style="width: 100%; height: 100%;">
                        <source src="${blobUrl}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                `;
                
                updateStatus('online', '✅ Blob播放成功');
                
                // 清理Blob URL
                const video = container.querySelector('video');
                video.onended = () => URL.revokeObjectURL(blobUrl);
                
            } catch (error) {
                updateStatus('offline', '❌ Blob播放失败');
                throw error;
            }
        }
        
        function playDirect(videoUrl, container) {
            container.innerHTML = `
                <video controls autoplay preload="metadata" style="width: 100%; height: 100%;">
                    <source src="${videoUrl}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            video.onerror = () => {
                throw new Error('直接播放失败，可能由于防盗链限制');
            };
        }
        
        function playWithIframe(videoUrl, container) {
            container.innerHTML = `
                <iframe src="${videoUrl}" style="width: 100%; height: 100%; border: none;" allowfullscreen></iframe>
            `;
        }
        
        function setPlayMethod(method) {
            currentPlayMethod = method;
            
            // 更新按钮状态
            document.querySelectorAll('.method-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`method-${method}`).classList.add('active');
            
            // 如果有当前视频，重新播放
            if (currentVideoId) {
                playVideo(currentVideoId);
            }
        }
        
        function changeQuality(quality) {
            currentQuality = quality;
            
            // 更新按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`btn-${quality}`).classList.add('active');
            
            // 如果有当前视频，重新播放
            if (currentVideoId) {
                playVideo(currentVideoId);
            }
        }
        
        function downloadCurrent() {
            if (!currentVideoId) {
                alert('请先选择一集');
                return;
            }
            const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url&level=${currentQuality}`;
            window.open(downloadUrl, '_blank');
        }
        
        function copyLink() {
            if (!currentVideoId) {
                alert('请先选择一集');
                return;
            }
            const videoUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=mp4&level=${currentQuality}`;
            navigator.clipboard.writeText(videoUrl).then(() => {
                alert('视频链接已复制到剪贴板');
            }).catch(() => {
                prompt('请手动复制视频链接:', videoUrl);
            });
        }
        
        function showVideoError(message) {
            const container = document.getElementById('videoContainer');
            container.innerHTML = `
                <div class="error">
                    <div style="font-size: 48px; margin-bottom: 15px;">😞</div>
                    <div>${message}</div>
                    <button onclick="playVideo(currentVideoId)" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                        重新播放
                    </button>
                </div>
            `;
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = `
                <div>
                    <div style="font-size: 48px; margin-bottom: 15px;">❌</div>
                    <h3>${message}</h3>
                    <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await initServiceWorker();
            await loadDrama();
        });
    </script>
</body>
</html>
