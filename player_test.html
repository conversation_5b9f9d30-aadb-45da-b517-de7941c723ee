<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放器测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        video { width: 100%; max-width: 600px; height: 400px; background: #000; }
        .playlist { margin-top: 20px; }
        .playlist li { padding: 10px; border: 1px solid #ddd; margin: 5px 0; cursor: pointer; }
        .playlist li.active { background: #007bff; color: white; }
        .info { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .error { color: red; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>播放器测试页面</h1>
        
        <div>
            <label>Book ID: </label>
            <input type="text" id="bookIdInput" value="7416545333695499326" style="width: 200px;">
            <button onclick="loadDrama()">加载短剧</button>
        </div>
        
        <div id="status" class="info">等待加载...</div>
        
        <div id="dramaInfo" class="info" style="display: none;">
            <h3 id="dramaTitle"></h3>
            <p id="dramaDesc"></p>
            <p id="dramaMeta"></p>
        </div>
        
        <video id="videoPlayer" controls></video>
        
        <div class="playlist">
            <h3>剧集列表</h3>
            <ul id="playlist"></ul>
        </div>
    </div>

    <script>
        console.log('播放器测试页面加载完成');
        
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        
        async function fetchData(params) {
            console.log('API请求参数:', params);
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            console.log('请求URL:', url.toString());
            
            try {
                const response = await fetch(url.toString());
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('API响应数据:', data);
                return data;
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const bookId = document.getElementById('bookIdInput').value.trim();
            const statusDiv = document.getElementById('status');
            const dramaInfoDiv = document.getElementById('dramaInfo');
            const playlistUl = document.getElementById('playlist');
            
            if (!bookId) {
                alert('请输入Book ID');
                return;
            }
            
            statusDiv.innerHTML = '<div class="loading">正在加载短剧信息...</div>';
            dramaInfoDiv.style.display = 'none';
            playlistUl.innerHTML = '';
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    statusDiv.innerHTML = '<div style="color: green;">短剧信息加载成功</div>';
                    
                    // 显示短剧信息
                    document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
                    document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
                    document.getElementById('dramaMeta').innerHTML = `
                        作者: ${data.author || '未知'} | 
                        分类: ${data.category || '未分类'} | 
                        总集数: ${data.total || data.data.length} | 
                        时长: ${data.duration || '未知'}
                    `;
                    dramaInfoDiv.style.display = 'block';
                    
                    // 显示剧集列表
                    data.data.forEach((video, index) => {
                        const li = document.createElement('li');
                        li.textContent = video.title || `第${index + 1}集`;
                        li.dataset.videoId = video.video_id;
                        li.addEventListener('click', () => {
                            document.querySelectorAll('#playlist li').forEach(item => item.classList.remove('active'));
                            li.classList.add('active');
                            playVideo(video.video_id);
                        });
                        playlistUl.appendChild(li);
                    });
                    
                    // 自动播放第一集
                    if (data.data.length > 0) {
                        playlistUl.firstChild.classList.add('active');
                        playVideo(data.data[0].video_id);
                    }
                    
                } else {
                    statusDiv.innerHTML = '<div class="error">加载失败: 数据格式错误</div>';
                    console.error('数据格式错误:', data);
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">加载失败: ' + error.message + '</div>';
                console.error('加载短剧失败:', error);
            }
        }
        
        async function playVideo(videoId) {
            console.log('开始播放视频:', videoId);
            const videoPlayer = document.getElementById('videoPlayer');
            const statusDiv = document.getElementById('status');
            
            statusDiv.innerHTML = '<div class="loading">正在加载视频...</div>';
            
            try {
                const videoData = await fetchData({ 
                    video_id: videoId, 
                    type: 'json',
                    level: '1080p'
                });
                
                if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                    console.log('获取到视频URL:', videoData.data.url);
                    videoPlayer.src = videoData.data.url;
                    statusDiv.innerHTML = '<div style="color: green;">视频加载成功</div>';
                    
                    try {
                        await videoPlayer.play();
                        console.log('视频开始播放');
                    } catch (playError) {
                        console.log('自动播放被阻止，需要用户点击播放');
                    }
                } else {
                    throw new Error('无法获取视频URL');
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="error">视频加载失败: ' + error.message + '</div>';
                console.error('播放视频失败:', error);
            }
        }
        
        // 页面加载完成后自动加载默认短剧
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面DOM加载完成');
            // 可以自动加载默认短剧
            // loadDrama();
        });
    </script>
</body>
</html>
