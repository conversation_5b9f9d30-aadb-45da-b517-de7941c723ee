const { getDuanjuDetails, renderPlayerPage, playVideo } = require('./player');
const api = require('./api');

jest.mock('./api');

// Mock the play method for HTMLMediaElement
window.HTMLMediaElement.prototype.play = () => { /* do nothing */ };

describe('player.js', () => {
  let mockElements;

  beforeEach(() => {
    document.body.innerHTML = `
      <video id="videoPlayer"></video>
      <h2 id="videoTitle"></h2>
      <p id="videoDescription"></p>
      <div id="playlist"></div>
    `;
    mockElements = {
      videoPlayer: document.getElementById('videoPlayer'),
      title: document.getElementById('videoTitle'),
      description: document.getElementById('videoDescription'),
      episodeList: document.getElementById('playlist'),
    };
  });

  afterEach(() => {
    document.body.innerHTML = '';
    jest.clearAllMocks();
  });

  describe('getDuanjuDetails', () => {
    it('should call api.fetchData with correct book_id', async () => {
      api.fetchData.mockResolvedValue({ data: { title: '', desc: '', list: [] } });
      await getDuanjuDetails('123');
      expect(api.fetchData).toHaveBeenCalledWith({ book_id: '123' });
    });
  });

  describe('renderPlayerPage', () => {
    it('should render player page with details', () => {
      const details = {
        title: 'Test', 
        desc: 'Desc',
        list: [{ video_id: 'v1', title: 'E1' }],
      };
      renderPlayerPage(details);
      expect(mockElements.title.textContent).toBe('Test');
      expect(mockElements.description.textContent).toBe('Desc');
      expect(mockElements.episodeList.innerHTML).toContain('E1');
    });
  });

  describe('playVideo', () => {
    it('should fetch video url and set it to video player', async () => {
      api.fetchData.mockResolvedValue({ data: { url: 'test.mp4' } });
      await playVideo('v1');
      expect(api.fetchData).toHaveBeenCalledWith({ video_id: 'v1', type: 'json' });
      expect(mockElements.videoPlayer.src).toBe('http://localhost/test.mp4');
    });
  });
});