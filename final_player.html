<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短剧播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .play-notice {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .play-notice h4 {
            margin: 0 0 10px 0;
            color: #155724;
            font-size: 18px;
        }
        .play-notice p {
            margin: 0;
            color: #155724;
            line-height: 1.5;
        }
        .copy-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        .action-btn {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .action-btn:hover { transform: translateY(-2px); }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .episode {
            padding: 15px;
            margin: 10px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(5px);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .episode-title {
            font-weight: 500;
        }
        .episode-actions {
            display: flex;
            gap: 10px;
        }
        .mini-btn {
            padding: 5px 10px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            color: inherit;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .mini-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .loading { text-align: center; color: #666; padding: 40px; }
        .error { text-align: center; color: #e74c3c; padding: 40px; }
        .success-msg {
            background: #d4edda;
            color: #155724;
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div id="loading" class="loading">正在加载短剧信息...</div>
        
        <div id="content" style="display: none;">
            <div id="dramaInfo" class="drama-info">
                <h2 id="dramaTitle">加载中...</h2>
                <p id="dramaDesc">正在加载剧集信息...</p>
                <div id="dramaMeta"></div>
            </div>
            
            <div class="play-notice">
                <h4>🎬 播放说明</h4>
                <p>由于防盗链保护，请点击下方"复制播放链接"按钮，然后在您的视频播放器中粘贴播放。</p>
                <p style="margin-top: 10px;"><strong>推荐播放器：</strong>PotPlayer、VLC Media Player、MPC-HC等</p>
                
                <div style="text-align: center; margin-top: 15px;">
                    <button class="copy-btn" onclick="copyCurrentVideo()">
                        📋 复制当前集播放链接
                    </button>
                </div>
                
                <div id="copySuccess" style="display: none;" class="success-msg">
                    ✅ 链接已复制到剪贴板！请在视频播放器中粘贴播放。
                </div>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="action-btn" onclick="downloadCurrent()">📥 下载当前集</button>
                <button class="action-btn" onclick="showVideoInfo()">ℹ️ 视频信息</button>
                <button class="action-btn" onclick="copyAllLinks()">📋 复制所有集链接</button>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        let currentVideoId = null;
        let currentVideoUrl = null;
        let currentEpisodeTitle = '';
        let dramaData = null;
        let allVideoUrls = {};
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                showError('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                    
                    // 默认选择第一集
                    if (data.data.length > 0) {
                        selectEpisode(data.data[0].video_id, 0, data.data[0].title);
                    }
                } else {
                    showError('无法加载短剧信息');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div class="episode-title">${video.title || `第${index + 1}集`}</div>
                    <div class="episode-actions">
                        <button class="mini-btn" onclick="event.stopPropagation(); copyEpisodeLink('${video.video_id}', '${video.title || `第${index + 1}集`}')">复制</button>
                        <button class="mini-btn" onclick="event.stopPropagation(); downloadEpisode('${video.video_id}')">下载</button>
                    </div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        async function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            currentEpisodeTitle = title;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
            });
            
            // 获取视频URL
            if (!allVideoUrls[videoId]) {
                try {
                    const videoData = await fetchData({ 
                        video_id: videoId, 
                        type: 'json',
                        level: '1080p'
                    });
                    
                    if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                        allVideoUrls[videoId] = videoData.data.url;
                        currentVideoUrl = videoData.data.url;
                    } else {
                        currentVideoUrl = null;
                        console.error('无法获取视频URL');
                    }
                } catch (error) {
                    console.error('获取视频URL失败:', error);
                    currentVideoUrl = null;
                }
            } else {
                currentVideoUrl = allVideoUrls[videoId];
            }
        }
        
        function copyCurrentVideo() {
            if (currentVideoUrl) {
                navigator.clipboard.writeText(currentVideoUrl).then(() => {
                    showCopySuccess();
                }).catch(() => {
                    prompt('请手动复制视频链接:', currentVideoUrl);
                });
            } else {
                alert('请先选择一集');
            }
        }
        
        async function copyEpisodeLink(videoId, title) {
            if (!allVideoUrls[videoId]) {
                try {
                    const videoData = await fetchData({ 
                        video_id: videoId, 
                        type: 'json',
                        level: '1080p'
                    });
                    
                    if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                        allVideoUrls[videoId] = videoData.data.url;
                    }
                } catch (error) {
                    console.error('获取视频URL失败:', error);
                    return;
                }
            }
            
            if (allVideoUrls[videoId]) {
                navigator.clipboard.writeText(allVideoUrls[videoId]).then(() => {
                    alert(`${title} 链接已复制到剪贴板`);
                }).catch(() => {
                    prompt(`请手动复制 ${title} 链接:`, allVideoUrls[videoId]);
                });
            }
        }
        
        function downloadCurrent() {
            if (currentVideoId) {
                const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url`;
                window.open(downloadUrl, '_blank');
            } else {
                alert('请先选择一集');
            }
        }
        
        function downloadEpisode(videoId) {
            const downloadUrl = `${API_BASE_URL}?video_id=${videoId}&type=url`;
            window.open(downloadUrl, '_blank');
        }
        
        function showVideoInfo() {
            if (currentVideoId && currentVideoUrl) {
                const info = `当前集：${currentEpisodeTitle}\n视频ID：${currentVideoId}\n播放链接：${currentVideoUrl}\n下载地址：${API_BASE_URL}?video_id=${currentVideoId}&type=url`;
                alert(info);
            } else {
                alert('请先选择一集');
            }
        }
        
        async function copyAllLinks() {
            if (!dramaData) return;
            
            let allLinks = `${dramaData.book_name} - 所有集播放链接\n\n`;
            
            for (let i = 0; i < dramaData.data.length; i++) {
                const video = dramaData.data[i];
                if (!allVideoUrls[video.video_id]) {
                    try {
                        const videoData = await fetchData({ 
                            video_id: video.video_id, 
                            type: 'json',
                            level: '1080p'
                        });
                        
                        if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                            allVideoUrls[video.video_id] = videoData.data.url;
                        }
                    } catch (error) {
                        console.error('获取视频URL失败:', error);
                    }
                }
                
                if (allVideoUrls[video.video_id]) {
                    allLinks += `${video.title || `第${i + 1}集`}：\n${allVideoUrls[video.video_id]}\n\n`;
                }
            }
            
            navigator.clipboard.writeText(allLinks).then(() => {
                alert('所有集链接已复制到剪贴板');
            }).catch(() => {
                const textarea = document.createElement('textarea');
                textarea.value = allLinks;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('所有集链接已复制到剪贴板');
            });
        }
        
        function showCopySuccess() {
            const successMsg = document.getElementById('copySuccess');
            successMsg.style.display = 'block';
            setTimeout(() => {
                successMsg.style.display = 'none';
            }, 3000);
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = `
                <h3>❌ ${message}</h3>
                <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                    重新加载
                </button>
            `;
        }
        
        // 页面加载完成后自动加载短剧
        document.addEventListener('DOMContentLoaded', loadDrama);
    </script>
</body>
</html>
