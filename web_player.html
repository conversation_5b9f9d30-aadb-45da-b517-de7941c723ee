<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .player-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
        }
        .video-player {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 10px;
        }
        .player-controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .control-btn {
            padding: 8px 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
        }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .episode {
            padding: 12px;
            margin: 8px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(5px);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .loading { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #666;
            font-size: 18px;
        }
        .error { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #e74c3c;
            font-size: 18px;
            text-align: center;
        }
        .play-methods {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        .method-btn {
            padding: 10px 15px;
            margin: 5px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }
        .method-btn.active {
            background: #28a745;
        }
        @media (max-width: 768px) {
            .player-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div id="loading" class="loading">正在加载短剧信息...</div>
        
        <div id="content" style="display: none;">
            <div class="player-container">
                <div class="video-section">
                    <div class="video-player" id="videoContainer">
                        <div class="loading">选择一集开始播放</div>
                    </div>
                    
                    <div class="play-methods">
                        <h4 style="margin: 0 0 10px 0;">🎬 播放方式</h4>
                        <button class="method-btn active" onclick="setPlayMethod('proxy')" id="proxyBtn">代理播放</button>
                        <button class="method-btn" onclick="setPlayMethod('iframe')" id="iframeBtn">iframe播放</button>
                        <button class="method-btn" onclick="setPlayMethod('direct')" id="directBtn">直接播放</button>
                        <button class="method-btn" onclick="setPlayMethod('hls')" id="hlsBtn">HLS播放</button>
                    </div>
                    
                    <div class="player-controls">
                        <button class="control-btn" onclick="changeQuality('360p')">360p</button>
                        <button class="control-btn" onclick="changeQuality('480p')">480p</button>
                        <button class="control-btn" onclick="changeQuality('720p')">720p</button>
                        <button class="control-btn" onclick="changeQuality('1080p')">1080p</button>
                        <button class="control-btn" onclick="changeQuality('2160p')">2160p</button>
                        <button class="control-btn" onclick="downloadCurrent()">📥 下载</button>
                        <button class="control-btn" onclick="copyLink()">📋 复制链接</button>
                    </div>
                </div>
                
                <div class="drama-info">
                    <h2 id="dramaTitle">加载中...</h2>
                    <p id="dramaDesc">正在加载剧集信息...</p>
                    <div id="dramaMeta"></div>
                </div>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        let currentVideoId = null;
        let currentVideoUrl = null;
        let currentQuality = '1080p';
        let playMethod = 'proxy';
        let dramaData = null;
        
        // 代理服务器列表（可以添加更多）
        const PROXY_SERVERS = [
            'https://cors-anywhere.herokuapp.com/',
            'https://api.allorigins.win/raw?url=',
            'https://corsproxy.io/?',
            ''  // 直接播放
        ];
        let currentProxyIndex = 0;
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                showError('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                } else {
                    showError('无法加载短剧信息');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${video.title || `第${index + 1}集`}</span>
                        <span style="font-size: 12px;">▶</span>
                    </div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        async function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
            });
            
            // 获取视频URL
            try {
                const videoData = await fetchData({ 
                    video_id: videoId, 
                    type: 'json',
                    level: currentQuality 
                });
                
                if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                    currentVideoUrl = videoData.data.url;
                    playVideo(currentVideoUrl);
                } else {
                    showVideoError('无法获取视频URL');
                }
            } catch (error) {
                console.error('获取视频URL失败:', error);
                showVideoError('获取视频失败: ' + error.message);
            }
        }
        
        function playVideo(url) {
            const container = document.getElementById('videoContainer');
            
            switch(playMethod) {
                case 'proxy':
                    playWithProxy(url, container);
                    break;
                case 'iframe':
                    playWithIframe(url, container);
                    break;
                case 'direct':
                    playDirect(url, container);
                    break;
                case 'hls':
                    playWithHLS(url, container);
                    break;
            }
        }
        
        function playWithProxy(url, container) {
            const proxyUrl = PROXY_SERVERS[currentProxyIndex] + encodeURIComponent(url);
            
            container.innerHTML = `
                <video controls autoplay style="width: 100%; height: 100%;">
                    <source src="${proxyUrl}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            video.onerror = () => {
                console.log(`代理 ${currentProxyIndex} 失败，尝试下一个`);
                currentProxyIndex = (currentProxyIndex + 1) % PROXY_SERVERS.length;
                if (currentProxyIndex === 0) {
                    showVideoError('所有代理都无法播放，请尝试其他播放方式');
                } else {
                    playWithProxy(url, container);
                }
            };
        }
        
        function playWithIframe(url, container) {
            container.innerHTML = `
                <iframe src="${url}" allowfullscreen style="width: 100%; height: 100%;"></iframe>
            `;
        }
        
        function playDirect(url, container) {
            container.innerHTML = `
                <video controls autoplay style="width: 100%; height: 100%;">
                    <source src="${url}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            video.onerror = () => {
                showVideoError('直接播放失败，可能由于防盗链限制');
            };
        }
        
        function playWithHLS(url, container) {
            // 尝试将MP4 URL转换为HLS格式（如果支持）
            const hlsUrl = url.replace('.mp4', '.m3u8');
            
            container.innerHTML = `
                <video controls autoplay style="width: 100%; height: 100%;">
                    <source src="${hlsUrl}" type="application/x-mpegURL">
                    <source src="${url}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            video.onerror = () => {
                showVideoError('HLS播放失败，请尝试其他播放方式');
            };
        }
        
        function setPlayMethod(method) {
            playMethod = method;
            
            // 更新按钮状态
            document.querySelectorAll('.method-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(method + 'Btn').classList.add('active');
            
            // 如果有当前视频，重新播放
            if (currentVideoUrl) {
                playVideo(currentVideoUrl);
            }
        }
        
        async function changeQuality(quality) {
            currentQuality = quality;
            if (currentVideoId) {
                const videoData = await fetchData({ 
                    video_id: currentVideoId, 
                    type: 'json',
                    level: quality 
                });
                
                if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                    currentVideoUrl = videoData.data.url;
                    playVideo(currentVideoUrl);
                }
            }
        }
        
        function downloadCurrent() {
            if (currentVideoId) {
                const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url`;
                window.open(downloadUrl, '_blank');
            } else {
                alert('请先选择一集');
            }
        }
        
        function copyLink() {
            if (currentVideoUrl) {
                navigator.clipboard.writeText(currentVideoUrl).then(() => {
                    alert('视频链接已复制到剪贴板');
                }).catch(() => {
                    prompt('请手动复制视频链接:', currentVideoUrl);
                });
            } else {
                alert('请先选择一集');
            }
        }
        
        function showVideoError(message) {
            const container = document.getElementById('videoContainer');
            container.innerHTML = `
                <div class="error">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 10px;">😞</div>
                        <div>${message}</div>
                        <button onclick="tryNextMethod()" style="margin-top: 15px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                            尝试其他播放方式
                        </button>
                    </div>
                </div>
            `;
        }
        
        function tryNextMethod() {
            const methods = ['proxy', 'iframe', 'direct', 'hls'];
            const currentIndex = methods.indexOf(playMethod);
            const nextIndex = (currentIndex + 1) % methods.length;
            setPlayMethod(methods[nextIndex]);
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = `
                <div>
                    <h3>❌ ${message}</h3>
                    <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
        
        // 页面加载完成后自动加载短剧
        document.addEventListener('DOMContentLoaded', loadDrama);
    </script>
</body>
</html>
