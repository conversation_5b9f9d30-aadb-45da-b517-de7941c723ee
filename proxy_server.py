#!/usr/bin/env python3
"""
简单的代理服务器，用于绕过视频防盗链
使用方法：python proxy_server.py
然后访问：http://localhost:8001/proxy?url=视频URL
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs, unquote
import urllib.request
import urllib.error
import json
import sys

class ProxyHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            parsed_path = urlparse(self.path)
            
            # 处理CORS预检请求
            if self.path == '/':
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>视频代理服务器</title>
                </head>
                <body>
                    <h1>🎬 视频代理服务器运行中</h1>
                    <p>使用方法：<code>http://localhost:8001/proxy?url=视频URL</code></p>
                    <p>状态：✅ 正常运行</p>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
                return
            
            # 代理视频请求
            if parsed_path.path == '/proxy':
                query_params = parse_qs(parsed_path.query)
                if 'url' not in query_params:
                    self.send_error(400, "缺少url参数")
                    return
                
                target_url = unquote(query_params['url'][0])
                print(f"代理请求: {target_url}")
                
                # 创建请求，模拟浏览器
                req = urllib.request.Request(target_url)
                req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
                req.add_header('Referer', 'https://www.douyin.com/')
                req.add_header('Accept', '*/*')
                req.add_header('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8')
                req.add_header('Accept-Encoding', 'identity')
                req.add_header('Connection', 'keep-alive')
                
                try:
                    with urllib.request.urlopen(req) as response:
                        # 设置响应头
                        self.send_response(200)
                        
                        # 复制重要的响应头
                        content_type = response.headers.get('Content-Type', 'video/mp4')
                        content_length = response.headers.get('Content-Length')
                        
                        self.send_header('Content-Type', content_type)
                        if content_length:
                            self.send_header('Content-Length', content_length)
                        
                        # CORS头
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                        
                        # 缓存控制
                        self.send_header('Cache-Control', 'no-cache')
                        
                        self.end_headers()
                        
                        # 流式传输视频数据
                        while True:
                            chunk = response.read(8192)
                            if not chunk:
                                break
                            self.wfile.write(chunk)
                        
                        print(f"代理成功: {target_url}")
                        
                except urllib.error.HTTPError as e:
                    print(f"HTTP错误 {e.code}: {target_url}")
                    self.send_error(e.code, f"上游服务器错误: {e.reason}")
                except urllib.error.URLError as e:
                    print(f"URL错误: {e.reason}")
                    self.send_error(502, f"无法连接到上游服务器: {e.reason}")
                except Exception as e:
                    print(f"代理错误: {str(e)}")
                    self.send_error(500, f"代理服务器内部错误: {str(e)}")
            else:
                self.send_error(404, "路径不存在")
                
        except Exception as e:
            print(f"处理请求时发生错误: {str(e)}")
            self.send_error(500, f"服务器内部错误: {str(e)}")
    
    def do_OPTIONS(self):
        # 处理CORS预检请求
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{self.date_time_string()}] {format % args}")

def run_server(port=8001):
    server_address = ('', port)
    httpd = HTTPServer(server_address, ProxyHandler)
    
    print(f"🎬 视频代理服务器启动成功！")
    print(f"📡 监听端口: {port}")
    print(f"🌐 访问地址: http://localhost:{port}")
    print(f"📖 使用方法: http://localhost:{port}/proxy?url=视频URL")
    print(f"⏹️  停止服务器: Ctrl+C")
    print("-" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    port = 8001
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("端口号必须是数字")
            sys.exit(1)
    
    run_server(port)
