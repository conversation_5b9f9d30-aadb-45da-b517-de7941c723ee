const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';

async function fetchData(params) {
    const url = new URL(API_BASE_URL);
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
    try {
        const response = await fetch(url.toString());
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error("Fetch error:", error);
        return null;
    }
}

module.exports = { fetchData };