// 浏览器和Node.js兼容的导入
let fetchData;
if (typeof require !== 'undefined') {
    fetchData = require('./api.js').fetchData;
} else if (typeof window !== 'undefined') {
    fetchData = window.fetchData;
}

// Shared constants and state
let currentPage = 1;
let currentSearchTerm = '';
let currentCategory = '';
let currentTab = 'search';
let searchHistory = [];
try {
    searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
} catch (e) {
    console.warn('localStorage不可用，搜索历史功能将被禁用');
}

// DOM elements (will be populated on DOMContentLoaded)
let searchInput, searchButton, resultsContainer, paginationContainer, loadingElement;

// --- App Object Definition ---

const app = {
    // --- Core Logic Functions ---
    async searchDuanju(name, page = 1) {
        currentSearchTerm = name;
        currentPage = page;
        
        // 显示加载状态
        this.showLoading();
        
        // 添加到搜索历史
        this.addToSearchHistory(name);
        
        const data = await fetchData({ name, page });
        this.hideLoading();
        
        if (data) {
            if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                this.renderResults(data.data);
                this.renderPagination(page, data.data.length >= 10); // 假设每页10条数据
            } else {
                this.showEmptyState(`没有找到"${name}"相关的结果`);
            }
        } else {
            this.showError('搜索失败，请稍后重试');
        }
    },

    async searchByCategory(category, offset = 1) {
        currentCategory = category;
        currentPage = offset;
        
        this.showLoading();
        const data = await fetchData({ classname: category, offset });
        this.hideLoading();
        
        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
            this.renderResults(data.data);
            this.renderPagination(offset, data.data.length >= 10, true); // 假设每页10条数据
        } else {
            this.showEmptyState(`暂无"${category}"类型的短剧`);
        }
    },

    renderResults(items) {
        resultsContainer.innerHTML = '';
        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'result-item';
            itemElement.dataset.bookId = item.book_id;

            // 处理封面图片URL，移除多余的参数
            let coverUrl = item.cover || '';
            if (coverUrl.includes('&cenguigui_api')) {
                coverUrl = coverUrl.split('&cenguigui_api')[0];
            }

            itemElement.innerHTML = `
                <img src="${coverUrl}" alt="${item.title}" loading="lazy" onerror="this.src='data:image/svg+xml,<svg xmlns=\\"http://www.w3.org/2000/svg\\" viewBox=\\"0 0 100 100\\"><text y=\\".9em\\" font-size=\\"90\\">🎬</text></svg>'">
                <div class="result-item-content">
                    <h3>${item.title}</h3>
                    <p>${item.intro || item.desc || '暂无简介'}</p>
                    <div style="font-size: 12px; color: #7f8c8d; margin-top: 8px;">
                        <span>📺 ${item.episode_cnt}集</span>
                        <span style="margin-left: 10px;">👥 ${item.play_cnt}播放</span>
                    </div>
                </div>
                <div class="play-overlay">▶</div>
            `;
            itemElement.addEventListener('click', () => this.navigateToPlayer(item.book_id));
            resultsContainer.appendChild(itemElement);
        });
    },

    renderPagination(page, hasMore, isCategory = false) {
        paginationContainer.innerHTML = '';
        if (page > 1) {
            const prevButton = document.createElement('button');
            prevButton.textContent = '上一页';
            prevButton.addEventListener('click', () => {
                if (isCategory) {
                    this.searchByCategory(currentCategory, page - 1);
                } else {
                    this.searchDuanju(currentSearchTerm, page - 1);
                }
            });
            paginationContainer.appendChild(prevButton);
        }
        
        const pageIndicator = document.createElement('span');
        pageIndicator.textContent = ` 第 ${page} 页 `;
        paginationContainer.appendChild(pageIndicator);

        if (hasMore) {
            const nextButton = document.createElement('button');
            nextButton.textContent = '下一页';
            nextButton.addEventListener('click', () => {
                if (isCategory) {
                    this.searchByCategory(currentCategory, page + 1);
                } else {
                    this.searchDuanju(currentSearchTerm, page + 1);
                }
            });
            paginationContainer.appendChild(nextButton);
        }
    },

    async loadRecommendations() {
        this.showLoading();
        const data = await fetchData({ type: 'recommend' });
        this.hideLoading();
        
        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
            this.renderResults(data.data);
            paginationContainer.innerHTML = '';
        } else {
            this.showEmptyState('暂无推荐内容');
        }
    },

    // --- UI Helper Functions ---
    showLoading() {
        if (loadingElement) {
            loadingElement.style.display = 'flex';
        }
        resultsContainer.innerHTML = '';
        paginationContainer.innerHTML = '';
    },

    hideLoading() {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    },

    showEmptyState(message) {
        resultsContainer.innerHTML = `
            <div class="empty-state">
                <h3>😔 ${message}</h3>
                <p>试试搜索其他关键词或浏览分类内容</p>
            </div>
        `;
        paginationContainer.innerHTML = '';
    },

    showError(message) {
        resultsContainer.innerHTML = `
            <div class="error-message">
                ❌ ${message}
            </div>
        `;
        paginationContainer.innerHTML = '';
    },

    // --- Search History Functions ---
    addToSearchHistory(term) {
        if (!term || searchHistory.includes(term)) return;
        
        searchHistory.unshift(term);
        if (searchHistory.length > 10) {
            searchHistory = searchHistory.slice(0, 10);
        }
        
        try {
            localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
        } catch (e) {
            console.warn('无法保存搜索历史');
        }
        this.renderSearchHistory();
    },

    renderSearchHistory() {
        const historyContainer = document.getElementById('search-history-tags');
        if (!historyContainer) return;
        
        historyContainer.innerHTML = '';
        searchHistory.slice(0, 5).forEach(term => {
            const tag = document.createElement('span');
            tag.className = 'search-tag';
            tag.textContent = term;
            tag.addEventListener('click', () => {
                searchInput.value = term;
                this.searchDuanju(term, 1);
            });
            historyContainer.appendChild(tag);
        });
    },

    // --- Tab Management ---
    switchTab(tabName) {
        currentTab = tabName;
        
        // 更新标签状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 显示对应内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
        
        // 加载对应内容
        if (tabName === 'recommend') {
            this.loadRecommendations();
        } else if (tabName === 'category') {
            // 默认加载推荐榜
            this.searchByCategory('推荐榜', 1);
        }
    },

    navigateToPlayer(book_id) {
        window.location.href = `player.html?book_id=${book_id}`;
    },

    // --- Main Application Setup ---
    initializeApp() {
        searchInput = document.getElementById('search-input');
        searchButton = document.getElementById('search-button');
        resultsContainer = document.getElementById('results-container');
        paginationContainer = document.getElementById('pagination');
        loadingElement = document.getElementById('loading');

        // 搜索功能
        searchButton.addEventListener('click', () => {
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                this.searchDuanju(searchTerm, 1);
            }
        });

        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                searchButton.click();
            }
        });

        // 标签切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchTab(tab.dataset.tab);
            });
        });

        // 分类按钮
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.searchByCategory(btn.dataset.category, 1);
            });
        });

        // 热门搜索标签
        document.querySelectorAll('#hot-search-tags .search-tag').forEach(tag => {
            tag.addEventListener('click', () => {
                searchInput.value = tag.textContent;
                this.searchDuanju(tag.textContent, 1);
            });
        });

        // 渲染搜索历史
        this.renderSearchHistory();
        
        // 默认加载推荐内容
        this.loadRecommendations();
    },

    // --- Helper for testing ---
    _setDomElements(elements) {
        searchInput = elements.searchInput;
        searchButton = elements.searchButton;
        resultsContainer = elements.resultsContainer;
        paginationContainer = elements.paginationContainer;
        loadingElement = elements.loadingElement;
    }
};

// --- Exports for testing ---
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { app };
}

// --- Browser Execution ---
// This ensures the app runs normally in the browser
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        // 确保fetchData已经加载
        if (typeof fetchData === 'undefined') {
            console.error('fetchData未定义，请确保api.js已正确加载');
            return;
        }
        app.initializeApp();
    });
}
