const { fetchData } = require('./api.js');

// Shared constants and state
let currentPage = 1;
let currentSearchTerm = '';

// DOM elements (will be populated on DOMContentLoaded)
let searchInput, searchButton, resultsContainer, paginationContainer;

// --- App Object Definition ---

const app = {
    // --- Core Logic Functions ---
    async searchDuanju(name, page = 1) {
        currentSearchTerm = name;
        currentPage = page;
        const data = await fetchData({ name, page });
        if (data) {
            if (data.data && data.data.list && data.data.list.length > 0) {
                this.renderResults(data.data.list);
                this.renderPagination(page, data.data.hasMore);
            } else {
                resultsContainer.innerHTML = `<p>没有找到“${name}”相关的结果。</p>`;
                paginationContainer.innerHTML = '';
            }
        }
    },

    renderResults(items) {
        resultsContainer.innerHTML = '';
        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'result-item';
            itemElement.dataset.bookId = item.book_id;
            itemElement.innerHTML = `
                <img src="${item.cover}" alt="${item.title}">
                <h3>${item.title}</h3>
            `;
            itemElement.addEventListener('click', () => this.navigateToPlayer(item.book_id));
            resultsContainer.appendChild(itemElement);
        });
    },

    renderPagination(page, hasMore) {
        paginationContainer.innerHTML = '';
        if (page > 1) {
            const prevButton = document.createElement('button');
            prevButton.textContent = '上一页';
            prevButton.addEventListener('click', () => {
                this.searchDuanju(currentSearchTerm, page - 1);
            });
            paginationContainer.appendChild(prevButton);
        }
        const pageIndicator = document.createElement('span');
        pageIndicator.textContent = ` 第 ${page} 页 `;
        paginationContainer.appendChild(pageIndicator);

        if (hasMore) {
            const nextButton = document.createElement('button');
            nextButton.textContent = '下一页';
            nextButton.addEventListener('click', () => {
                this.searchDuanju(currentSearchTerm, page + 1);
            });
            paginationContainer.appendChild(nextButton);
        }
    },

    async loadRecommendations() {
        const data = await fetchData({ type: 'recommend' });
        if (data && data.data && data.data.list) {
            this.renderResults(data.data.list);
        }
    },

    navigateToPlayer(book_id) {
        window.location.href = `player.html?book_id=${book_id}`;
    },

    // --- Main Application Setup ---
    initializeApp() {
        searchInput = document.getElementById('search-input');
        searchButton = document.getElementById('search-button');
        resultsContainer = document.getElementById('results-container');
        paginationContainer = document.getElementById('pagination');

        searchButton.addEventListener('click', () => {
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                this.searchDuanju(searchTerm, 1);
            }
        });

        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                searchButton.click();
            }
        });

        this.loadRecommendations();
    },

    // --- Helper for testing ---
    _setDomElements(elements) {
        searchInput = elements.searchInput;
        searchButton = elements.searchButton;
        resultsContainer = elements.resultsContainer;
        paginationContainer = elements.paginationContainer;
    }
};

// --- Exports for testing ---
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { app };
}

// --- Browser Execution ---
// This ensures the app runs normally in the browser
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => app.initializeApp());
}