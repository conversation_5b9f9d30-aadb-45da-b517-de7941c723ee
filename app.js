const { fetchData } = require('./api.js');

// Shared constants and state
let currentPage = 1;
let currentSearchTerm = '';
let currentCategory = '';
let currentTab = 'search';
let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');

// DOM elements (will be populated on DOMContentLoaded)
let searchInput, searchButton, resultsContainer, paginationContainer, loadingElement;

// --- App Object Definition ---

const app = {
    // --- Core Logic Functions ---
    async searchDuanju(name, page = 1) {
        currentSearchTerm = name;
        currentPage = page;
        
        // 显示加载状态
        this.showLoading();
        
        // 添加到搜索历史
        this.addToSearchHistory(name);
        
        const data = await fetchData({ name, page });
        this.hideLoading();
        
        if (data) {
            if (data.data && data.data.list && data.data.list.length > 0) {
                this.renderResults(data.data.list);
                this.renderPagination(page, data.data.hasMore);
            } else {
                this.showEmptyState(`没有找到"${name}"相关的结果`);
            }
        } else {
            this.showError('搜索失败，请稍后重试');
        }
    },

    async searchByCategory(category, offset = 1) {
        currentCategory = category;
        currentPage = offset;
        
        this.showLoading();
        const data = await fetchData({ classname: category, offset });
        this.hideLoading();
        
        if (data && data.data && data.data.list && data.data.list.length > 0) {
            this.renderResults(data.data.list);
            this.renderPagination(offset, data.data.hasMore, true);
        } else {
            this.showEmptyState(`暂无"${category}"类型的短剧`);
        }
    },

    renderResults(items) {
        resultsContainer.innerHTML = '';
        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'result-item';
            itemElement.dataset.bookId = item.book_id;
            itemElement.innerHTML = `
                <img src="${item.cover}" alt="${item.title}" loading="lazy">
                <div class="result-item-content">
                    <h3>${item.title}</h3>
                    <p>${item.desc || '暂无简介'}</p>
                </div>
                <div class="play-overlay">▶</div>
            `;
            itemElement.addEventListener('click', () => this.navigateToPlayer(item.book_id));
            resultsContainer.appendChild(itemElement);
        });
    },

    renderPagination(page, hasMore, isCategory = false) {
        paginationContainer.innerHTML = '';
        if (page > 1) {
            const prevButton = document.createElement('button');
            prevButton.textContent = '上一页';
            prevButton.addEventListener('click', () => {
                if (isCategory) {
                    this.searchByCategory(currentCategory, page - 1);
                } else {
                    this.searchDuanju(currentSearchTerm, page - 1);
                }
            });
            paginationContainer.appendChild(prevButton);
        }
        
        const pageIndicator = document.createElement('span');
        pageIndicator.textContent = ` 第 ${page} 页 `;
        paginationContainer.appendChild(pageIndicator);

        if (hasMore) {
            const nextButton = document.createElement('button');
            nextButton.textContent = '下一页';
            nextButton.addEventListener('click', () => {
                if (isCategory) {
                    this.searchByCategory(currentCategory, page + 1);
                } else {
                    this.searchDuanju(currentSearchTerm, page + 1);
                }
            });
            paginationContainer.appendChild(nextButton);
        }
    },

    async loadRecommendations() {
        this.showLoading();
        const data = await fetchData({ type: 'recommend' });
        this.hideLoading();
        
        if (data && data.data && data.data.list) {
            this.renderResults(data.data.list);
            paginationContainer.innerHTML = '';
        } else {
            this.showEmptyState('暂无推荐内容');
        }
    },

    // --- UI Helper Functions ---
    showLoading() {
        if (loadingElement) {
            loadingElement.style.display = 'flex';
        }
        resultsContainer.innerHTML = '';
        paginationContainer.innerHTML = '';
    },

    hideLoading() {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    },

    showEmptyState(message) {
        resultsContainer.innerHTML = `
            <div class="empty-state">
                <h3>😔 ${message}</h3>
                <p>试试搜索其他关键词或浏览分类内容</p>
            </div>
        `;
        paginationContainer.innerHTML = '';
    },

    showError(message) {
        resultsContainer.innerHTML = `
            <div class="error-message">
                ❌ ${message}
            </div>
        `;
        paginationContainer.innerHTML = '';
    },

    // --- Search History Functions ---
    addToSearchHistory(term) {
        if (!term || searchHistory.includes(term)) return;
        
        searchHistory.unshift(term);
        if (searchHistory.length > 10) {
            searchHistory = searchHistory.slice(0, 10);
        }
        
        localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
        this.renderSearchHistory();
    },

    renderSearchHistory() {
        const historyContainer = document.getElementById('search-history-tags');
        if (!historyContainer) return;
        
        historyContainer.innerHTML = '';
        searchHistory.slice(0, 5).forEach(term => {
            const tag = document.createElement('span');
            tag.className = 'search-tag';
            tag.textContent = term;
            tag.addEventListener('click', () => {
                searchInput.value = term;
                this.searchDuanju(term, 1);
            });
            historyContainer.appendChild(tag);
        });
    },

    // --- Tab Management ---
    switchTab(tabName) {
        currentTab = tabName;
        
        // 更新标签状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 显示对应内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
        
        // 加载对应内容
        if (tabName === 'recommend') {
            this.loadRecommendations();
        } else if (tabName === 'category') {
            // 默认加载推荐榜
            this.searchByCategory('推荐榜', 1);
        }
    },

    navigateToPlayer(book_id) {
        window.location.href = `player.html?book_id=${book_id}`;
    },

    // --- Main Application Setup ---
    initializeApp() {
        searchInput = document.getElementById('search-input');
        searchButton = document.getElementById('search-button');
        resultsContainer = document.getElementById('results-container');
        paginationContainer = document.getElementById('pagination');
        loadingElement = document.getElementById('loading');

        // 搜索功能
        searchButton.addEventListener('click', () => {
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                this.searchDuanju(searchTerm, 1);
            }
        });

        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                searchButton.click();
            }
        });

        // 标签切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchTab(tab.dataset.tab);
            });
        });

        // 分类按钮
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.searchByCategory(btn.dataset.category, 1);
            });
        });

        // 热门搜索标签
        document.querySelectorAll('#hot-search-tags .search-tag').forEach(tag => {
            tag.addEventListener('click', () => {
                searchInput.value = tag.textContent;
                this.searchDuanju(tag.textContent, 1);
            });
        });

        // 渲染搜索历史
        this.renderSearchHistory();
        
        // 默认加载推荐内容
        this.loadRecommendations();
    },

    // --- Helper for testing ---
    _setDomElements(elements) {
        searchInput = elements.searchInput;
        searchButton = elements.searchButton;
        resultsContainer = elements.resultsContainer;
        paginationContainer = elements.paginationContainer;
        loadingElement = elements.loadingElement;
    }
};

// --- Exports for testing ---
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { app };
}

// --- Browser Execution ---
// This ensures the app runs normally in the browser
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => app.initializeApp());
}
