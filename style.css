body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f4f4f9;
    margin: 0;
    padding: 20px;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    text-align: center;
    color: #4a4a4a;
}

.search-box {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#search-input {
    width: 60%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 16px;
}

#search-button {
    padding: 10px 20px;
    border: none;
    background-color: #007bff;
    color: white;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 16px;
}

#search-button:hover {
    background-color: #0056b3;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.result-item {
    background: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s;
}

.result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.result-item img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.result-item h3 {
    margin: 10px 0 5px;
    font-size: 16px;
}

.result-item p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

#pagination {
    text-align: center;
    margin-top: 20px;
}

#pagination button {
    padding: 8px 16px;
    margin: 0 5px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
}

#pagination button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    position: relative;
}

.close-button {
    color: #aaa;
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-button:hover,
.close-button:focus {
    color: black;
}

#modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

.video-list {
    list-style: none;
    padding: 0;
}

.video-list li {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.video-list li:hover {
    background-color: #f0f0f0;
}

#video-player-container {
    margin-top: 20px;
}

#video-player {
    width: 100%;
}