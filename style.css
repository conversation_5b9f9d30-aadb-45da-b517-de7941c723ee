/* 现代化设计 - 使用渐变背景和更好的字体 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

/* 主标题样式 */
h1 {
    text-align: center;
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    text-align: center;
    color: #7f8c8d;
    font-size: 1.1rem;
    margin-bottom: 30px;
}

/* 导航栏 */
.nav-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #ecf0f1;
}

.nav-tab {
    padding: 12px 24px;
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.nav-tab:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

/* 搜索框区域 */
.search-section {
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
}

#search-input {
    width: 60%;
    padding: 15px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 25px 0 0 25px;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    background: #fff;
}

#search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#search-button {
    padding: 15px 25px;
    border: none;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 0 25px 25px 0;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

#search-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 搜索历史和热门搜索 */
.search-suggestions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.search-history, .hot-searches {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.search-history h4, .hot-searches h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
}

.search-tag {
    display: inline-block;
    padding: 5px 12px;
    margin: 3px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

/* 分类选择器 */
.category-selector {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.category-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid #e1e8ed;
    border-radius: 20px;
    color: #2c3e50;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.category-btn:hover {
    border-color: #667eea;
    transform: translateY(-1px);
}

/* 结果网格 - 现代化卡片设计 */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.result-item {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 20px;
    padding: 0;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s ease;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    position: relative;
}

.result-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.result-item img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    border-radius: 20px 20px 0 0;
    transition: all 0.3s ease;
}

.result-item:hover img {
    transform: scale(1.05);
}

.result-item-content {
    padding: 20px;
}

.result-item h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
}

.result-item p {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
    line-height: 1.4;
}

.result-item .play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(102, 126, 234, 0.9);
    color: white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    opacity: 0;
    transition: all 0.3s ease;
}

.result-item:hover .play-overlay {
    opacity: 1;
}

/* 分页样式 */
#pagination {
    text-align: center;
    margin-top: 30px;
}

#pagination button {
    padding: 12px 20px;
    margin: 0 8px;
    border: 2px solid #e1e8ed;
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

#pagination button:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

#pagination button.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

#pagination span {
    padding: 12px 20px;
    color: #7f8c8d;
    font-weight: 500;
}

/* 标签内容切换 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    position: relative;
}

.close-button {
    color: #aaa;
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-button:hover,
.close-button:focus {
    color: black;
}

#modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

.video-list {
    list-style: none;
    padding: 0;
}

.video-list li {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.video-list li:hover {
    background-color: #f0f0f0;
}

/* 播放器页面样式 */
.player-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.video-player {
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

#videoPlayer {
    width: 100%;
    height: auto;
    min-height: 400px;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.quality-selector {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
}

.video-details {
    background: rgba(255, 255, 255, 0.9);
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.video-details h2 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 700;
}

.video-details p {
    color: #7f8c8d;
    line-height: 1.6;
    margin: 0;
}

.playlist-container {
    background: rgba(255, 255, 255, 0.9);
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.playlist-container h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
}

#playlist {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 400px;
    overflow-y: auto;
}

#playlist li {
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

#playlist li:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    transform: translateX(5px);
}

#playlist li.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }

    .player-container {
        grid-template-columns: 1fr;
    }

    #search-input {
        width: 70%;
    }

    .results-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 15px;
    }

    .category-selector {
        gap: 5px;
    }

    .category-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e1e8ed;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-message {
    text-align: center;
    padding: 40px;
    color: #e74c3c;
    font-size: 18px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    color: #2c3e50;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
}