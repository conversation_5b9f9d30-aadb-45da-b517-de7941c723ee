<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短剧聚合搜索 - 海量短剧资源</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
</head>
<body>
    <div class="container">
        <h1>🎬 短剧聚合搜索</h1>
        <p class="subtitle">汇聚番茄、蛋花、西瓜、悟空等多平台优质短剧资源</p>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="search">🔍 搜索</button>
            <button class="nav-tab" data-tab="category">📂 分类</button>
            <button class="nav-tab" data-tab="recommend">🔥 推荐</button>
        </div>

        <!-- 搜索页面 -->
        <div id="search-tab" class="tab-content active">
            <div class="search-section">
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="输入短剧名称，如：总裁、穿越、霸总...">
                    <button id="search-button">🔍 搜索</button>
                </div>

                <!-- 搜索建议 -->
                <div class="search-suggestions">
                    <div class="search-history">
                        <h4>🕒 搜索历史</h4>
                        <div id="search-history-tags"></div>
                    </div>
                    <div class="hot-searches">
                        <h4>🔥 热门搜索</h4>
                        <div id="hot-search-tags">
                            <span class="search-tag">总裁</span>
                            <span class="search-tag">穿越</span>
                            <span class="search-tag">霸总</span>
                            <span class="search-tag">重生</span>
                            <span class="search-tag">现代言情</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类页面 -->
        <div id="category-tab" class="tab-content">
            <div class="category-selector">
                <button class="category-btn active" data-category="推荐榜">🏆 推荐榜</button>
                <button class="category-btn" data-category="新剧">🆕 新剧</button>
                <button class="category-btn" data-category="逆袭">💪 逆袭</button>
                <button class="category-btn" data-category="霸总">👔 霸总</button>
                <button class="category-btn" data-category="现代言情">💕 现代言情</button>
                <button class="category-btn" data-category="打脸虐渣">😤 打脸虐渣</button>
                <button class="category-btn" data-category="豪门恩怨">🏰 豪门恩怨</button>
                <button class="category-btn" data-category="穿越">🌀 穿越</button>
                <button class="category-btn" data-category="重生">🔄 重生</button>
                <button class="category-btn" data-category="古装">👘 古装</button>
                <button class="category-btn" data-category="都市修仙">🏙️ 都市修仙</button>
                <button class="category-btn" data-category="赘婿逆袭">🤵 赘婿逆袭</button>
            </div>
        </div>

        <!-- 推荐页面 -->
        <div id="recommend-tab" class="tab-content">
            <div class="empty-state">
                <h3>🎯 为您推荐</h3>
                <p>精选优质短剧，每日更新</p>
            </div>
        </div>

        <!-- 结果容器 -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
        </div>

        <div id="results-container" class="results-grid"></div>
        <div id="pagination"></div>
    </div>

    <script src="api.js"></script>
    <script src="app.js"></script>
</body>
</html>