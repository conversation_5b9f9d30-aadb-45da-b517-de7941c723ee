// Service Worker for bypassing CORS and referer restrictions
const CACHE_NAME = 'video-proxy-cache-v1';

// Install event
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    self.skipWaiting();
});

// Activate event
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    event.waitUntil(self.clients.claim());
});

// Fetch event - intercept network requests
self.addEventListener('fetch', event => {
    const url = new URL(event.request.url);
    
    // Only handle video requests
    if (url.searchParams.has('proxy_video')) {
        event.respondWith(handleVideoRequest(event.request));
    }
});

async function handleVideoRequest(request) {
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('proxy_video');
    
    if (!targetUrl) {
        return new Response('Missing target URL', { status: 400 });
    }
    
    try {
        // Create a new request with modified headers
        const modifiedRequest = new Request(targetUrl, {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.douyin.com/',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'identity',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'video',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'cross-site'
            },
            mode: 'cors',
            credentials: 'omit'
        });
        
        // Fetch the video
        const response = await fetch(modifiedRequest);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // Create a new response with CORS headers
        const modifiedResponse = new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: {
                'Content-Type': response.headers.get('Content-Type') || 'video/mp4',
                'Content-Length': response.headers.get('Content-Length') || '',
                'Accept-Ranges': 'bytes',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
                'Access-Control-Allow-Headers': 'Range, Content-Range',
                'Cache-Control': 'public, max-age=3600'
            }
        });
        
        return modifiedResponse;
        
    } catch (error) {
        console.error('Service Worker fetch error:', error);
        return new Response(`Proxy Error: ${error.message}`, { 
            status: 502,
            headers: {
                'Content-Type': 'text/plain',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}
