<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .player-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
        }
        .video-player {
            width: 100%;
            height: 450px;
            background: #000;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 15px;
        }
        .player-controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .control-btn {
            padding: 10px 18px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .control-btn.active {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            max-height: 500px;
            overflow-y: auto;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .episode {
            padding: 15px;
            margin: 10px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .episode-title {
            font-weight: 600;
            font-size: 16px;
        }
        .episode-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
        }
        .loading { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 450px;
            color: #666;
            font-size: 18px;
            flex-direction: column;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e8ed;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 450px;
            color: #e74c3c;
            font-size: 18px;
            text-align: center;
            flex-direction: column;
        }
        .success-notice {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
            text-align: center;
        }
        @media (max-width: 768px) {
            .player-container {
                grid-template-columns: 1fr;
            }
            .video-player {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            正在加载短剧信息...
        </div>
        
        <div id="content" style="display: none;">
            <div class="player-container">
                <div class="video-section">
                    <div class="video-player" id="videoContainer">
                        <div class="loading">
                            <div class="spinner"></div>
                            选择一集开始播放
                        </div>
                    </div>
                    
                    <div class="success-notice">
                        🎉 使用API直接播放功能，无需代理服务器！
                    </div>
                    
                    <div class="player-controls">
                        <button class="control-btn active" onclick="changeQuality('1080p')" id="btn-1080p">1080p 超清</button>
                        <button class="control-btn" onclick="changeQuality('720p')" id="btn-720p">720p 高清</button>
                        <button class="control-btn" onclick="changeQuality('480p')" id="btn-480p">480p 标清</button>
                        <button class="control-btn" onclick="changeQuality('360p')" id="btn-360p">360p 流畅</button>
                        <button class="control-btn" onclick="downloadCurrent()">📥 下载</button>
                        <button class="control-btn" onclick="copyLink()">📋 复制链接</button>
                    </div>
                </div>
                
                <div class="drama-info">
                    <h2 id="dramaTitle">加载中...</h2>
                    <p id="dramaDesc">正在加载剧集信息...</p>
                    <div id="dramaMeta"></div>
                </div>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        let currentVideoId = null;
        let currentQuality = '1080p';
        let dramaData = null;
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                showError('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                    
                    // 默认播放第一集
                    if (data.data.length > 0) {
                        selectEpisode(data.data[0].video_id, 0, data.data[0].title || '第1集');
                    }
                } else {
                    showError('无法加载短剧信息');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div class="episode-title">${video.title || `第${index + 1}集`}</div>
                    <div class="episode-status">▶</div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
                const status = ep.querySelector('.episode-status');
                if (i === index) {
                    status.textContent = '播放中';
                } else {
                    status.textContent = '▶';
                }
            });
            
            // 播放视频
            playVideo(videoId);
        }
        
        function playVideo(videoId) {
            const container = document.getElementById('videoContainer');
            
            // 使用API的type=mp4直接播放功能
            const videoUrl = `${API_BASE_URL}?video_id=${videoId}&type=mp4&level=${currentQuality}`;
            
            container.innerHTML = `
                <video controls autoplay preload="metadata" style="width: 100%; height: 100%;">
                    <source src="${videoUrl}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            
            video.onloadstart = () => {
                console.log('开始加载视频:', videoUrl);
            };
            
            video.oncanplay = () => {
                console.log('视频可以播放');
            };
            
            video.onerror = (e) => {
                console.error('视频播放错误:', e);
                showVideoError('视频播放失败，可能是网络问题或视频不存在');
            };
            
            video.onended = () => {
                // 自动播放下一集
                const currentIndex = Array.from(document.querySelectorAll('.episode')).findIndex(ep => ep.classList.contains('active'));
                const nextIndex = currentIndex + 1;
                if (nextIndex < dramaData.data.length) {
                    const nextVideo = dramaData.data[nextIndex];
                    selectEpisode(nextVideo.video_id, nextIndex, nextVideo.title || `第${nextIndex + 1}集`);
                }
            };
        }
        
        function changeQuality(quality) {
            currentQuality = quality;
            
            // 更新按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${quality}`).classList.add('active');
            
            // 如果有当前视频，重新播放
            if (currentVideoId) {
                const currentTime = document.querySelector('video')?.currentTime || 0;
                playVideo(currentVideoId);
                
                // 恢复播放位置
                setTimeout(() => {
                    const video = document.querySelector('video');
                    if (video && currentTime > 0) {
                        video.currentTime = currentTime;
                    }
                }, 1000);
            }
        }
        
        function downloadCurrent() {
            if (currentVideoId) {
                const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url&level=${currentQuality}`;
                window.open(downloadUrl, '_blank');
            } else {
                alert('请先选择一集');
            }
        }
        
        function copyLink() {
            if (currentVideoId) {
                const videoUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=mp4&level=${currentQuality}`;
                navigator.clipboard.writeText(videoUrl).then(() => {
                    alert('视频链接已复制到剪贴板');
                }).catch(() => {
                    prompt('请手动复制视频链接:', videoUrl);
                });
            } else {
                alert('请先选择一集');
            }
        }
        
        function showVideoError(message) {
            const container = document.getElementById('videoContainer');
            container.innerHTML = `
                <div class="error">
                    <div style="font-size: 48px; margin-bottom: 15px;">😞</div>
                    <div>${message}</div>
                    <button onclick="playVideo(currentVideoId)" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                        重新播放
                    </button>
                </div>
            `;
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = `
                <div>
                    <div style="font-size: 48px; margin-bottom: 15px;">❌</div>
                    <h3>${message}</h3>
                    <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
        
        // 页面加载完成后自动加载短剧
        document.addEventListener('DOMContentLoaded', loadDrama);
    </script>
</body>
</html>
