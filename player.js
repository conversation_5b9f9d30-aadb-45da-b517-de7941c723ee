const { fetchData } = require('./api.js');

if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        const urlParams = new URLSearchParams(window.location.search);
        const bookId = urlParams.get('book_id');

        if (bookId) {
            getDuanjuDetails(bookId);
        }
    });
}

async function getDuanjuDetails(book_id) {
    const data = await fetchData({ book_id });
    if (data && data.data) {
        renderPlayerPage(data.data);
        if (data.data.list && data.data.list.length > 0) {
            playVideo(data.data.list[0].video_id);
        }
    }
}

function renderPlayerPage(details) {
    document.getElementById('videoTitle').textContent = details.title;
    document.getElementById('videoDescription').textContent = details.desc;

    const playlist = document.getElementById('playlist');
    playlist.innerHTML = '';
    details.list.forEach(video => {
        const li = document.createElement('li');
        li.textContent = video.title;
        li.dataset.videoId = video.video_id;
        li.addEventListener('click', () => playVideo(video.video_id));
        playlist.appendChild(li);
    });
}

async function playVideo(videoId) {
    const videoPlayer = document.getElementById('videoPlayer');
    videoPlayer.src = ''; // Clear previous source

    try {
        const videoData = await fetchData({ video_id: videoId, type: 'json' });
        if (videoData && videoData.data && videoData.data.url) {
            videoPlayer.src = videoData.data.url;
            videoPlayer.play();
        } else {
            throw new Error('Could not resolve video URL.');
        }
    } catch (error) {
        console.error("Error playing video:", error);
        // Optionally display an error message to the user in the player
    }
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { getDuanjuDetails, renderPlayerPage, playVideo };
}