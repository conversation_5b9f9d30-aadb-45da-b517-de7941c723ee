// 浏览器和Node.js兼容的导入
let fetchData;
if (typeof require !== 'undefined') {
    fetchData = require('./api.js').fetchData;
} else if (typeof window !== 'undefined') {
    fetchData = window.fetchData;
}

// 播放器状态
let currentVideoId = null;
let currentQuality = '1080p';
let duanjuData = null;

if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('播放器页面加载完成');

        // 确保fetchData已经加载
        if (typeof fetchData === 'undefined') {
            console.error('fetchData未定义，请确保api.js已正确加载');
            showError('页面加载失败，请刷新重试');
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        const bookId = urlParams.get('book_id');
        console.log('获取到book_id:', bookId);

        if (bookId) {
            initializePlayer();
            getDuanjuDetails(bookId);
        } else {
            showError('缺少短剧ID参数');
        }
    });
}

async function getDuanjuDetails(book_id) {
    console.log('开始获取短剧详情，book_id:', book_id);

    try {
        const data = await fetchData({ book_id });
        console.log('API返回数据:', data);

        if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
            console.log('数据解析成功，视频数量:', data.data.length);

            // API返回的数据结构：data是视频列表数组，其他信息在根级别
            duanjuData = {
                list: data.data,
                title: data.book_name,
                desc: data.desc,
                author: data.author,
                category: data.category,
                total: data.total,
                duration: data.duration,
                book_pic: data.book_pic
            };

            console.log('处理后的数据:', duanjuData);
            renderPlayerPage(duanjuData);

            // 播放第一集
            if (data.data.length > 0) {
                console.log('开始播放第一集:', data.data[0].video_id);
                playVideo(data.data[0].video_id);
            }
        } else {
            console.error('数据格式错误:', data);
            showError('无法加载短剧信息 - 数据格式错误');
        }
    } catch (error) {
        console.error('获取短剧详情失败:', error);
        showError('加载失败，请稍后重试: ' + error.message);
    }
}

async function getActorInfo(series_id) {
    try {
        const data = await fetchData({ series_id });
        if (data && data.data && data.data.actors) {
            renderActorInfo(data.data.actors);
        }
    } catch (error) {
        console.error('获取演员信息失败:', error);
    }
}

function renderPlayerPage(details) {
    document.getElementById('videoTitle').textContent = details.title || '未知标题';
    document.getElementById('videoDescription').textContent = details.desc || '暂无简介';

    // 更新元信息
    const metaElement = document.getElementById('videoMeta');
    if (metaElement) {
        metaElement.innerHTML = `
            <span>📺 共 ${details.total || (details.list ? details.list.length : 0)} 集</span>
            <span style="margin-left: 20px;">🏷️ ${details.category || '未分类'}</span>
            <span style="margin-left: 20px;">⏱️ ${details.duration || '暂无时长'}</span>
            <span style="margin-left: 20px;">👤 ${details.author || '未知作者'}</span>
        `;
    }

    // 更新剧集数量
    const episodeCount = document.getElementById('episodeCount');
    if (episodeCount) {
        episodeCount.textContent = details.total || (details.list ? details.list.length : 0);
    }

    const playlist = document.getElementById('playlist');
    playlist.innerHTML = '';

    if (details.list && details.list.length > 0) {
        details.list.forEach((video, index) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>${video.title || `第${index + 1}集`}</span>
                    <span style="font-size: 12px; color: #7f8c8d;">▶</span>
                </div>
            `;
            li.dataset.videoId = video.video_id;
            li.addEventListener('click', () => {
                // 更新当前播放状态
                document.querySelectorAll('#playlist li').forEach(item => item.classList.remove('active'));
                li.classList.add('active');
                playVideo(video.video_id);
            });
            playlist.appendChild(li);
        });

        // 默认选中第一集
        if (playlist.firstChild) {
            playlist.firstChild.classList.add('active');
        }
    }
}

function renderActorInfo(actors) {
    const actorList = document.getElementById('actorList');
    if (!actorList || !actors || actors.length === 0) return;

    actorList.innerHTML = actors.map(actor => `
        <span style="display: inline-block; margin: 5px; padding: 5px 10px; background: rgba(102, 126, 234, 0.1); border-radius: 15px; font-size: 12px;">
            ${actor.name || actor}
        </span>
    `).join('');
}

async function playVideo(videoId) {
    console.log('开始播放视频，video_id:', videoId);
    currentVideoId = videoId;
    const videoPlayer = document.getElementById('videoPlayer');

    if (!videoPlayer) {
        console.error('找不到视频播放器元素');
        return;
    }

    // 显示加载状态
    videoPlayer.poster = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">⏳</text></svg>';

    try {
        console.log('请求视频数据，参数:', { video_id: videoId, type: 'json', level: currentQuality });

        const videoData = await fetchData({
            video_id: videoId,
            type: 'json',
            level: currentQuality
        });

        console.log('视频API返回数据:', videoData);

        if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
            console.log('获取到视频URL:', videoData.data.url);
            videoPlayer.src = videoData.data.url;
            videoPlayer.poster = ''; // 清除加载图标

            // 尝试自动播放
            try {
                await videoPlayer.play();
                console.log('视频开始播放');
            } catch (playError) {
                console.log('自动播放被阻止，需要用户交互:', playError);
            }
        } else {
            console.error('视频数据格式错误:', videoData);
            throw new Error('无法解析视频URL - 数据格式错误');
        }
    } catch (error) {
        console.error("播放视频失败:", error);
        showVideoError('视频加载失败: ' + error.message);
    }
}

async function changeQuality(quality) {
    currentQuality = quality;
    if (currentVideoId) {
        const currentTime = document.getElementById('videoPlayer').currentTime;
        await playVideo(currentVideoId);

        // 恢复播放位置
        setTimeout(() => {
            document.getElementById('videoPlayer').currentTime = currentTime;
        }, 1000);
    }
}

function downloadVideo() {
    if (currentVideoId) {
        const downloadUrl = `https://api.cenguigui.cn/api/duanju/api.php?video_id=${currentVideoId}&type=url`;
        window.open(downloadUrl, '_blank');
    }
}

function initializePlayer() {
    // 画质选择器
    const qualitySelector = document.getElementById('qualitySelector');
    if (qualitySelector) {
        qualitySelector.addEventListener('change', (e) => {
            changeQuality(e.target.value);
        });
    }

    // 下载按钮
    const downloadBtn = document.getElementById('downloadBtn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', downloadVideo);
    }

    // 视频播放器事件
    const videoPlayer = document.getElementById('videoPlayer');
    if (videoPlayer) {
        videoPlayer.addEventListener('error', () => {
            showVideoError('视频播放出错');
        });

        videoPlayer.addEventListener('loadstart', () => {
            console.log('开始加载视频');
        });

        videoPlayer.addEventListener('canplay', () => {
            console.log('视频可以播放');
        });
    }
}

function showError(message) {
    const container = document.querySelector('.container');
    container.innerHTML = `
        <div class="error-message">
            <h2>❌ ${message}</h2>
            <button onclick="history.back()" style="margin-top: 20px; padding: 10px 20px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 20px; cursor: pointer;">
                返回搜索
            </button>
        </div>
    `;
}

function showVideoError(message) {
    const videoPlayer = document.getElementById('videoPlayer');
    videoPlayer.style.display = 'none';

    const playerContainer = document.querySelector('.video-player');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'video-error';
    errorDiv.style.cssText = 'display: flex; align-items: center; justify-content: center; height: 400px; background: #f8f9fa; color: #6c757d; font-size: 18px;';
    errorDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="font-size: 48px; margin-bottom: 10px;">😞</div>
            <div>${message}</div>
            <button onclick="location.reload()" style="margin-top: 15px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                重新加载
            </button>
        </div>
    `;

    // 移除之前的错误信息
    const existingError = playerContainer.querySelector('.video-error');
    if (existingError) {
        existingError.remove();
    }

    playerContainer.appendChild(errorDiv);
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { getDuanjuDetails, renderPlayerPage, playVideo };
}