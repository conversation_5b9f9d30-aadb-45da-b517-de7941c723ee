<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .notice-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }
        .notice-card h3 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }
        .notice-card p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .proxy-status {
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .player-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .video-player {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .fake-player {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 15px;
        }
        .fake-player:hover {
            border-color: #667eea;
            transform: scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        .fake-player.selected {
            border-color: #28a745;
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        .player-content {
            z-index: 2;
        }
        .play-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        .play-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .play-subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        .one-click-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }
        .action-card {
            background: rgba(255,255,255,0.9);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }
        .action-card:hover::before {
            left: 100%;
        }
        .action-card:hover {
            border-color: #667eea;
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }
        .action-card.primary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            transform: scale(1.05);
        }
        .action-card.primary:hover {
            transform: scale(1.08) translateY(-8px);
            border-color: #28a745;
        }
        .action-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        .action-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .action-desc {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
            max-height: 500px;
            overflow-y: auto;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .episode {
            padding: 18px;
            margin: 12px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        .episode::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.3s;
        }
        .episode:hover::before {
            left: 100%;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(10px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        .episode.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transform: scale(1.02);
        }
        .episode-info {
            flex: 1;
            z-index: 2;
        }
        .episode-title {
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .episode-meta {
            font-size: 13px;
            opacity: 0.7;
        }
        .episode-actions {
            display: flex;
            gap: 10px;
            z-index: 2;
        }
        .mini-btn {
            padding: 8px 15px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            color: inherit;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .mini-btn:hover {
            background: rgba(255,255,255,0.4);
            transform: translateY(-2px);
        }
        .quality-bar {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255,255,255,0.6);
            border-radius: 25px;
        }
        .quality-btn {
            padding: 10px 20px;
            background: rgba(255,255,255,0.8);
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .quality-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .quality-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .success-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(40, 167, 69, 0.3);
            z-index: 1000;
            text-align: center;
            display: none;
            animation: popIn 0.3s ease;
        }
        @keyframes popIn {
            0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }
        .loading { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #666;
            font-size: 18px;
            flex-direction: column;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #e1e8ed;
            border-top: 5px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        @media (max-width: 768px) {
            .player-container {
                grid-template-columns: 1fr;
            }
            .one-click-actions {
                grid-template-columns: 1fr;
            }
            .fake-player {
                height: 250px;
            }
            .quality-bar {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div class="notice-card">
            <h3>🎬 智能播放器</h3>
            <p>支持网页直接播放和外部播放器播放，为您提供最佳观看体验。</p>
        </div>

        <div class="proxy-status">
            <span class="status-indicator" id="proxyStatus"></span>
            <span id="proxyStatusText">正在检测代理服务器...</span>
            <button onclick="checkProxyStatus()" style="margin-left: 15px; padding: 5px 12px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 12px;">重新检测</button>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            正在加载短剧信息...
        </div>
        
        <div id="content" style="display: none;">
            <div class="player-container">
                <div class="video-section">
                    <div id="videoContainer">
                        <div class="fake-player" id="fakePlayer" onclick="togglePlayMode()">
                            <div class="player-content">
                                <div class="play-icon">🎬</div>
                                <div class="play-title" id="currentTitle">选择一集开始播放</div>
                                <div class="play-subtitle" id="playSubtitle">点击切换播放模式</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="quality-bar">
                        <button class="quality-btn" onclick="setQuality('360p')" id="q-360p">360p 流畅</button>
                        <button class="quality-btn" onclick="setQuality('480p')" id="q-480p">480p 标清</button>
                        <button class="quality-btn" onclick="setQuality('720p')" id="q-720p">720p 高清</button>
                        <button class="quality-btn active" onclick="setQuality('1080p')" id="q-1080p">1080p 超清</button>
                        <button class="quality-btn" onclick="setQuality('2160p')" id="q-2160p">2160p 4K</button>
                    </div>
                    
                    <div class="one-click-actions">
                        <div class="action-card primary" onclick="webPlay()" id="webPlayCard">
                            <span class="action-icon">🌐</span>
                            <div class="action-title">网页播放</div>
                            <div class="action-desc">在浏览器中直接播放（需代理）</div>
                        </div>

                        <div class="action-card" onclick="quickPlay()">
                            <span class="action-icon">⚡</span>
                            <div class="action-title">一键播放</div>
                            <div class="action-desc">复制链接到外部播放器</div>
                        </div>
                        
                        <div class="action-card" onclick="openVLC()">
                            <span class="action-icon">🎮</span>
                            <div class="action-title">VLC播放</div>
                            <div class="action-desc">尝试在VLC播放器中打开</div>
                        </div>
                        
                        <div class="action-card" onclick="openPotPlayer()">
                            <span class="action-icon">🎯</span>
                            <div class="action-title">PotPlayer播放</div>
                            <div class="action-desc">推荐使用PotPlayer播放</div>
                        </div>
                        
                        <div class="action-card" onclick="batchCopy()">
                            <span class="action-icon">📋</span>
                            <div class="action-title">批量复制</div>
                            <div class="action-desc">复制所有集的播放链接</div>
                        </div>
                        
                        <div class="action-card" onclick="downloadCurrent()">
                            <span class="action-icon">📥</span>
                            <div class="action-title">下载视频</div>
                            <div class="action-desc">下载当前集到本地</div>
                        </div>
                        
                        <div class="action-card" onclick="generateM3U()">
                            <span class="action-icon">📺</span>
                            <div class="action-title">生成播放列表</div>
                            <div class="action-desc">创建M3U播放列表文件</div>
                        </div>
                    </div>
                </div>
                
                <div class="drama-info">
                    <h2 id="dramaTitle">加载中...</h2>
                    <p id="dramaDesc">正在加载剧集信息...</p>
                    <div id="dramaMeta"></div>
                </div>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div class="success-popup" id="successPopup">
            <div style="font-size: 48px; margin-bottom: 15px;">✅</div>
            <div style="font-size: 20px; font-weight: bold; margin-bottom: 10px;">操作成功！</div>
            <div id="successMessage">链接已复制到剪贴板</div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/dunju/api.php';
        let currentVideoId = null;
        let currentQuality = '1080p';
        let currentTitle = '';
        let dramaData = null;
        const PROXY_URL = 'http://localhost:8001/proxy';
        let proxyOnline = false;
        let isWebPlayMode = false;
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function checkProxyStatus() {
            try {
                const response = await fetch(PROXY_URL.replace('/proxy', '/'), {
                    method: 'GET',
                    mode: 'cors'
                });

                if (response.ok) {
                    proxyOnline = true;
                    updateProxyStatus('online', '✅ 代理服务器在线，支持网页播放');
                    document.getElementById('webPlayCard').style.opacity = '1';
                } else {
                    throw new Error('代理服务器响应异常');
                }
            } catch (error) {
                proxyOnline = false;
                updateProxyStatus('offline', '❌ 代理服务器离线，仅支持外部播放');
                document.getElementById('webPlayCard').style.opacity = '0.5';
                console.error('代理服务器检测失败:', error);
            }
        }

        function updateProxyStatus(status, text) {
            const indicator = document.getElementById('proxyStatus');
            const statusText = document.getElementById('proxyStatusText');

            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                alert('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                    
                    // 默认选择第一集
                    if (data.data.length > 0) {
                        selectEpisode(data.data[0].video_id, 0, data.data[0].title || '第1集');
                    }
                } else {
                    alert('无法加载短剧信息');
                }
            } catch (error) {
                alert('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div class="episode-info">
                        <div class="episode-title">${video.title || `第${index + 1}集`}</div>
                        <div class="episode-meta">点击选择 • 时长未知</div>
                    </div>
                    <div class="episode-actions">
                        <button class="mini-btn" onclick="event.stopPropagation(); quickCopyEpisode('${video.video_id}', '${video.title || `第${index + 1}集`}')">复制</button>
                        <button class="mini-btn" onclick="event.stopPropagation(); quickDownload('${video.video_id}')">下载</button>
                    </div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            currentTitle = title;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
                const meta = ep.querySelector('.episode-meta');
                if (i === index) {
                    meta.textContent = '当前选中 • 准备播放';
                } else {
                    meta.textContent = '点击选择 • 时长未知';
                }
            });
            
            // 更新播放器显示
            document.getElementById('currentTitle').textContent = title;
            document.getElementById('fakePlayer').classList.add('selected');

            // 如果是网页播放模式，自动播放
            if (isWebPlayMode && proxyOnline) {
                webPlay();
            }
        }

        function togglePlayMode() {
            if (proxyOnline) {
                if (isWebPlayMode) {
                    // 切换到外部播放模式
                    showFakePlayer();
                } else {
                    // 切换到网页播放模式
                    webPlay();
                }
            } else {
                // 代理离线，只能外部播放
                quickPlay();
            }
        }

        function showFakePlayer() {
            isWebPlayMode = false;
            const container = document.getElementById('videoContainer');
            container.innerHTML = `
                <div class="fake-player" id="fakePlayer" onclick="togglePlayMode()">
                    <div class="player-content">
                        <div class="play-icon">🎬</div>
                        <div class="play-title" id="currentTitle">${currentTitle || '选择一集开始播放'}</div>
                        <div class="play-subtitle">点击网页播放</div>
                    </div>
                </div>
            `;
        }

        async function webPlay() {
            if (!currentVideoId) {
                alert('请先选择一集');
                return;
            }

            if (!proxyOnline) {
                alert('代理服务器未启动，无法网页播放');
                return;
            }

            isWebPlayMode = true;
            const container = document.getElementById('videoContainer');

            // 显示加载状态
            container.innerHTML = `
                <div class="video-player">
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 18px;">
                        <div style="text-align: center;">
                            <div class="spinner" style="margin: 0 auto 15px auto;"></div>
                            正在加载视频...
                        </div>
                    </div>
                </div>
            `;

            try {
                // 获取视频URL
                const videoData = await fetchData({
                    video_id: currentVideoId,
                    type: 'json',
                    level: currentQuality
                });

                if (!videoData || videoData.code !== 200 || !videoData.data || !videoData.data.url) {
                    throw new Error('无法获取视频URL');
                }

                const videoUrl = videoData.data.url;
                const proxyUrl = `${PROXY_URL}?url=${encodeURIComponent(videoUrl)}`;

                // 创建视频播放器
                container.innerHTML = `
                    <div class="video-player">
                        <video controls autoplay preload="metadata" style="width: 100%; height: 100%;">
                            <source src="${proxyUrl}" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                        <div style="position: absolute; top: 10px; right: 10px; z-index: 10;">
                            <button onclick="showFakePlayer()" style="padding: 5px 10px; background: rgba(0,0,0,0.7); color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 12px;">
                                退出网页播放
                            </button>
                        </div>
                    </div>
                `;

                const video = container.querySelector('video');
                video.onerror = () => {
                    showVideoError('网页播放失败，请尝试外部播放');
                };

                video.onended = () => {
                    // 自动播放下一集
                    const currentIndex = Array.from(document.querySelectorAll('.episode')).findIndex(ep => ep.classList.contains('active'));
                    const nextIndex = currentIndex + 1;
                    if (nextIndex < dramaData.data.length) {
                        const nextVideo = dramaData.data[nextIndex];
                        selectEpisode(nextVideo.video_id, nextIndex, nextVideo.title || `第${nextIndex + 1}集`);
                    }
                };

                showSuccess('网页播放成功！');

            } catch (error) {
                console.error('网页播放失败:', error);
                showVideoError('网页播放失败: ' + error.message);
            }
        }

        function showVideoError(message) {
            const container = document.getElementById('videoContainer');
            container.innerHTML = `
                <div class="fake-player">
                    <div class="player-content">
                        <div style="font-size: 48px; margin-bottom: 15px;">😞</div>
                        <div style="font-size: 18px; margin-bottom: 15px;">${message}</div>
                        <button onclick="showFakePlayer()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                            返回播放器
                        </button>
                    </div>
                </div>
            `;
        }
        
        function setQuality(quality) {
            currentQuality = quality;
            
            // 更新按钮状态
            document.querySelectorAll('.quality-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`q-${quality}`).classList.add('active');
        }
        
        function getCurrentVideoUrl() {
            if (!currentVideoId) return null;
            return `${API_BASE_URL}?video_id=${currentVideoId}&type=mp4&level=${currentQuality}`;
        }
        
        function quickPlay() {
            const url = getCurrentVideoUrl();
            if (!url) {
                alert('请先选择一集');
                return;
            }
            
            // 复制链接
            navigator.clipboard.writeText(url).then(() => {
                showSuccess('链接已复制！请在播放器中粘贴播放');
                
                // 显示播放指引
                setTimeout(() => {
                    alert(`🎬 播放指引：\n\n1. 链接已复制到剪贴板\n2. 打开PotPlayer、VLC等播放器\n3. 按Ctrl+V粘贴播放\n\n当前播放：${currentTitle} (${currentQuality})`);
                }, 1000);
                
            }).catch(() => {
                prompt('请手动复制播放链接:', url);
            });
        }
        
        function openVLC() {
            const url = getCurrentVideoUrl();
            if (!url) {
                alert('请先选择一集');
                return;
            }
            
            // 尝试打开VLC
            const vlcUrl = `vlc://${url}`;
            window.open(vlcUrl, '_blank');
            
            // 备用方案
            setTimeout(() => {
                navigator.clipboard.writeText(url).then(() => {
                    showSuccess('已尝试打开VLC，同时复制了链接作为备用');
                });
            }, 1000);
        }
        
        function openPotPlayer() {
            const url = getCurrentVideoUrl();
            if (!url) {
                alert('请先选择一集');
                return;
            }
            
            // PotPlayer协议
            const potUrl = `potplayer://${url}`;
            window.open(potUrl, '_blank');
            
            // 备用方案
            setTimeout(() => {
                navigator.clipboard.writeText(url).then(() => {
                    showSuccess('已尝试打开PotPlayer，同时复制了链接作为备用');
                });
            }, 1000);
        }
        
        function batchCopy() {
            if (!dramaData) return;
            
            let allLinks = `${dramaData.book_name} - 全集播放链接 (${currentQuality})\n`;
            allLinks += `生成时间：${new Date().toLocaleString()}\n\n`;
            
            dramaData.data.forEach((video, index) => {
                const url = `${API_BASE_URL}?video_id=${video.video_id}&type=mp4&level=${currentQuality}`;
                allLinks += `${index + 1}. ${video.title || `第${index + 1}集`}\n${url}\n\n`;
            });
            
            navigator.clipboard.writeText(allLinks).then(() => {
                showSuccess(`已复制全部${dramaData.data.length}集的播放链接`);
            }).catch(() => {
                const textarea = document.createElement('textarea');
                textarea.value = allLinks;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showSuccess('批量复制完成');
            });
        }
        
        function downloadCurrent() {
            if (!currentVideoId) {
                alert('请先选择一集');
                return;
            }
            const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url&level=${currentQuality}`;
            window.open(downloadUrl, '_blank');
            showSuccess('已打开下载链接');
        }
        
        function quickDownload(videoId) {
            const downloadUrl = `${API_BASE_URL}?video_id=${videoId}&type=url&level=${currentQuality}`;
            window.open(downloadUrl, '_blank');
        }
        
        function quickCopyEpisode(videoId, title) {
            const url = `${API_BASE_URL}?video_id=${videoId}&type=mp4&level=${currentQuality}`;
            navigator.clipboard.writeText(url).then(() => {
                showSuccess(`${title} 链接已复制`);
            });
        }
        
        function generateM3U() {
            if (!dramaData) return;
            
            let m3uContent = '#EXTM3U\n';
            m3uContent += `#PLAYLIST:${dramaData.book_name}\n\n`;
            
            dramaData.data.forEach((video, index) => {
                const url = `${API_BASE_URL}?video_id=${video.video_id}&type=mp4&level=${currentQuality}`;
                m3uContent += `#EXTINF:-1,${video.title || `第${index + 1}集`}\n`;
                m3uContent += `${url}\n\n`;
            });
            
            // 下载M3U文件
            const blob = new Blob([m3uContent], { type: 'audio/x-mpegurl' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${dramaData.book_name}.m3u`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showSuccess('M3U播放列表已生成并下载');
        }
        
        function showSuccess(message) {
            const popup = document.getElementById('successPopup');
            const messageEl = document.getElementById('successMessage');
            
            messageEl.textContent = message;
            popup.style.display = 'block';
            
            setTimeout(() => {
                popup.style.display = 'none';
            }, 3000);
        }
        
        // 页面加载完成后自动加载短剧和检测代理
        document.addEventListener('DOMContentLoaded', async () => {
            await checkProxyStatus();
            await loadDrama();
        });
    </script>
</body>
</html>
