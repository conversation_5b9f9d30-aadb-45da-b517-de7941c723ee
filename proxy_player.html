<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理播放器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .setup-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .setup-notice h3 {
            margin: 0 0 15px 0;
            color: #856404;
        }
        .setup-notice ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .setup-notice li {
            margin: 8px 0;
            color: #856404;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            color: #495057;
        }
        .player-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .video-section {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
        }
        .video-player {
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .player-controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .control-btn {
            padding: 8px 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        .drama-info {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
        }
        .playlist {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .episode {
            padding: 12px;
            margin: 8px 0;
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .episode:hover {
            border-color: #667eea;
            transform: translateX(5px);
        }
        .episode.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .loading { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #666;
            font-size: 18px;
        }
        .error { 
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #e74c3c;
            font-size: 18px;
            text-align: center;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        @media (max-width: 768px) {
            .player-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-btn">← 返回搜索</a>
        
        <div class="setup-notice">
            <h3>🔧 代理服务器设置说明</h3>
            <p>为了在浏览器中播放视频，需要启动本地代理服务器：</p>
            <ol>
                <li>打开命令行/终端</li>
                <li>进入项目目录</li>
                <li>运行代理服务器：</li>
            </ol>
            <div class="code-block">python proxy_server.py</div>
            <p>代理服务器状态：<span class="status-indicator" id="proxyStatus"></span><span id="proxyStatusText">检测中...</span></p>
            <button onclick="checkProxyStatus()" style="padding: 8px 15px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer;">重新检测</button>
        </div>
        
        <div id="loading" class="loading">正在加载短剧信息...</div>
        
        <div id="content" style="display: none;">
            <div class="player-container">
                <div class="video-section">
                    <div class="video-player" id="videoContainer">
                        <div class="loading">选择一集开始播放</div>
                    </div>
                    
                    <div class="player-controls">
                        <button class="control-btn" onclick="changeQuality('360p')">360p</button>
                        <button class="control-btn" onclick="changeQuality('480p')">480p</button>
                        <button class="control-btn" onclick="changeQuality('720p')">720p</button>
                        <button class="control-btn" onclick="changeQuality('1080p')">1080p</button>
                        <button class="control-btn" onclick="changeQuality('2160p')">2160p</button>
                        <button class="control-btn" onclick="downloadCurrent()">📥 下载</button>
                        <button class="control-btn" onclick="copyLink()">📋 复制链接</button>
                        <button class="control-btn" onclick="openInVLC()">🎬 VLC播放</button>
                    </div>
                </div>
                
                <div class="drama-info">
                    <h2 id="dramaTitle">加载中...</h2>
                    <p id="dramaDesc">正在加载剧集信息...</p>
                    <div id="dramaMeta"></div>
                </div>
            </div>
            
            <div class="playlist">
                <h3>📺 剧集列表 (<span id="episodeCount">0</span>集)</h3>
                <div id="playlist"></div>
            </div>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'https://api.cenguigui.cn/api/duanju/api.php';
        const PROXY_URL = 'http://localhost:8001/proxy';
        let currentVideoId = null;
        let currentVideoUrl = null;
        let currentQuality = '1080p';
        let dramaData = null;
        let proxyOnline = false;
        
        async function fetchData(params) {
            const url = new URL(API_BASE_URL);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
            
            try {
                const response = await fetch(url.toString());
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error("API请求错误:", error);
                return null;
            }
        }
        
        async function checkProxyStatus() {
            try {
                const response = await fetch(PROXY_URL.replace('/proxy', '/'), {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    proxyOnline = true;
                    document.getElementById('proxyStatus').className = 'status-indicator status-online';
                    document.getElementById('proxyStatusText').textContent = '✅ 代理服务器在线';
                } else {
                    throw new Error('代理服务器响应异常');
                }
            } catch (error) {
                proxyOnline = false;
                document.getElementById('proxyStatus').className = 'status-indicator status-offline';
                document.getElementById('proxyStatusText').textContent = '❌ 代理服务器离线';
                console.error('代理服务器检测失败:', error);
            }
        }
        
        async function loadDrama() {
            const urlParams = new URLSearchParams(window.location.search);
            const bookId = urlParams.get('book_id');
            
            if (!bookId) {
                showError('缺少短剧ID参数');
                return;
            }
            
            try {
                const data = await fetchData({ book_id: bookId });
                
                if (data && data.code === 200 && data.data && Array.isArray(data.data)) {
                    dramaData = data;
                    renderDrama(data);
                } else {
                    showError('无法加载短剧信息');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        function renderDrama(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 显示短剧信息
            document.getElementById('dramaTitle').textContent = data.book_name || '未知标题';
            document.getElementById('dramaDesc').textContent = data.desc || '暂无简介';
            document.getElementById('dramaMeta').innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px; font-size: 14px; color: #7f8c8d;">
                    <span>👤 ${data.author || '未知作者'}</span>
                    <span>🏷️ ${data.category || '未分类'}</span>
                    <span>📺 ${data.total || data.data.length}集</span>
                    <span>⏱️ ${data.duration || '未知时长'}</span>
                </div>
            `;
            
            // 显示剧集列表
            document.getElementById('episodeCount').textContent = data.total || data.data.length;
            const playlist = document.getElementById('playlist');
            playlist.innerHTML = '';
            
            data.data.forEach((video, index) => {
                const episode = document.createElement('div');
                episode.className = 'episode';
                episode.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${video.title || `第${index + 1}集`}</span>
                        <span style="font-size: 12px;">▶</span>
                    </div>
                `;
                episode.onclick = () => selectEpisode(video.video_id, index, video.title || `第${index + 1}集`);
                playlist.appendChild(episode);
            });
        }
        
        async function selectEpisode(videoId, index, title) {
            currentVideoId = videoId;
            
            // 更新选中状态
            document.querySelectorAll('.episode').forEach((ep, i) => {
                ep.classList.toggle('active', i === index);
            });
            
            // 获取视频URL
            try {
                const videoData = await fetchData({ 
                    video_id: videoId, 
                    type: 'json',
                    level: currentQuality 
                });
                
                if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                    currentVideoUrl = videoData.data.url;
                    playVideo(currentVideoUrl);
                } else {
                    showVideoError('无法获取视频URL');
                }
            } catch (error) {
                console.error('获取视频URL失败:', error);
                showVideoError('获取视频失败: ' + error.message);
            }
        }
        
        function playVideo(url) {
            const container = document.getElementById('videoContainer');
            
            if (!proxyOnline) {
                showVideoError('代理服务器未启动，请先启动代理服务器');
                return;
            }
            
            const proxyUrl = `${PROXY_URL}?url=${encodeURIComponent(url)}`;
            
            container.innerHTML = `
                <video controls autoplay style="width: 100%; height: 100%;">
                    <source src="${proxyUrl}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
            `;
            
            const video = container.querySelector('video');
            video.onerror = () => {
                showVideoError('视频播放失败，可能是代理服务器问题');
            };
            
            video.onloadstart = () => {
                console.log('开始加载视频:', proxyUrl);
            };
            
            video.oncanplay = () => {
                console.log('视频可以播放');
            };
        }
        
        async function changeQuality(quality) {
            currentQuality = quality;
            if (currentVideoId) {
                const videoData = await fetchData({ 
                    video_id: currentVideoId, 
                    type: 'json',
                    level: quality 
                });
                
                if (videoData && videoData.code === 200 && videoData.data && videoData.data.url) {
                    currentVideoUrl = videoData.data.url;
                    playVideo(currentVideoUrl);
                }
            }
        }
        
        function downloadCurrent() {
            if (currentVideoId) {
                const downloadUrl = `${API_BASE_URL}?video_id=${currentVideoId}&type=url`;
                window.open(downloadUrl, '_blank');
            } else {
                alert('请先选择一集');
            }
        }
        
        function copyLink() {
            if (currentVideoUrl) {
                navigator.clipboard.writeText(currentVideoUrl).then(() => {
                    alert('视频链接已复制到剪贴板');
                }).catch(() => {
                    prompt('请手动复制视频链接:', currentVideoUrl);
                });
            } else {
                alert('请先选择一集');
            }
        }
        
        function openInVLC() {
            if (currentVideoUrl) {
                // 尝试打开VLC播放器
                const vlcUrl = `vlc://${currentVideoUrl}`;
                window.open(vlcUrl, '_blank');
                
                // 同时提供手动复制选项
                setTimeout(() => {
                    if (confirm('如果VLC没有自动打开，是否复制链接手动播放？')) {
                        copyLink();
                    }
                }, 2000);
            } else {
                alert('请先选择一集');
            }
        }
        
        function showVideoError(message) {
            const container = document.getElementById('videoContainer');
            container.innerHTML = `
                <div class="error">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 10px;">😞</div>
                        <div>${message}</div>
                        <button onclick="checkProxyStatus()" style="margin-top: 15px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                            重新检测代理
                        </button>
                    </div>
                </div>
            `;
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = `
                <div>
                    <h3>❌ ${message}</h3>
                    <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 15px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
        
        // 页面加载完成后自动加载短剧和检测代理
        document.addEventListener('DOMContentLoaded', () => {
            checkProxyStatus();
            loadDrama();
        });
    </script>
</body>
</html>
