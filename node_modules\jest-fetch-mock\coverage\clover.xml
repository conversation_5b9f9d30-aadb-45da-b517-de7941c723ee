<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1538489080692" clover="3.2.0">
  <project timestamp="1538489080692" name="All files">
    <metrics statements="11" coveredstatements="9" conditionals="6" coveredconditionals="4" methods="7" coveredmethods="5" elements="24" coveredelements="18" complexity="0" loc="11" ncloc="11" packages="1" files="1" classes="1">
      <file name="api.js" path="/Users/<USER>/Projects/packages/jest-fetch-mock/tests/api.js">
        <metrics statements="11" coveredstatements="9" conditionals="6" coveredconditionals="4" methods="7" coveredmethods="5"/>
        <line num="4" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="5" count="3" type="stmt"/>
        <line num="6" count="3" type="stmt"/>
        <line num="8" count="3" type="stmt"/>
        <line num="9" count="3" type="stmt"/>
        <line num="10" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="18" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
      </file>
    </metrics>
  </project>
</coverage>
